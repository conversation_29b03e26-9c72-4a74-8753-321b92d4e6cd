public with sharing class ClientSalesMetricsUtil {
    public static Map<Id, Map<String, Object>> calculateMetrics(List<Id> accountIds) {
        Map<Id, Map<String, Object>> results = new Map<Id, Map<String, Object>>();
        if (accountIds == null || accountIds.isEmpty()) return results;

        // Query Accounts
        Map<Id, Account> accounts = new Map<Id, Account>([
            SELECT Id, Name, Tenant_ID__c FROM Account WHERE Id IN :accountIds
        ]);

        // Query Subscriptions
        Map<Id, List<KGRenewal__Subscription__c>> subsByAccount = new Map<Id, List<KGRenewal__Subscription__c>>();
        for (KGRenewal__Subscription__c sub : [
            SELECT Id, KGRenewal__Account__c, KGRenewal__TotalAmount__c, KGRenewal__RenewalAmount__c, KGRenewal__Amount__c,
                   <PERSON>GR<PERSON>wal__AmountPaid__c, <PERSON><PERSON><PERSON>wal__AmountRemaining__c, <PERSON><PERSON><PERSON>wal__RenewalOpportunity__c,
                   <PERSON><PERSON><PERSON><PERSON>__StartDate__c, <PERSON><PERSON><PERSON><PERSON>__EndDate__c, <PERSON><PERSON><PERSON>wal__RenewalDate__c, KGRenewal__RenewalTerm__c,
                   KGRenewal__IsAutomaticBillingEnabled__c, KGRenewal__BillingFrequency__c, KGRenewal__BillingPeriod__c
            FROM KGRenewal__Subscription__c
            WHERE KGRenewal__Account__c IN :accountIds
              AND KGRenewal__IsActiveTerm__c = true
        ]) {
            if (!subsByAccount.containsKey(sub.KGRenewal__Account__c)) {
                subsByAccount.put(sub.KGRenewal__Account__c, new List<KGRenewal__Subscription__c>());
            }
            subsByAccount.get(sub.KGRenewal__Account__c).add(sub);
        }

        // Query Opportunities
        Map<Id, List<Opportunity>> oppsByAccount = new Map<Id, List<Opportunity>>();
        for (Opportunity opp : [
            SELECT Id, AccountId, Amount, StageName, Name, CloseDate,
                   KGRenewal__ContractEffectiveDate__c, KGRenewal__ContractEndDate__c, KGRenewal__Term__c,
                   Targeted_Construction_Volume_TCV__c
            FROM Opportunity
            WHERE AccountId IN :accountIds
        ]) {
            if (!oppsByAccount.containsKey(opp.AccountId)) {
                oppsByAccount.put(opp.AccountId, new List<Opportunity>());
            }
            oppsByAccount.get(opp.AccountId).add(opp);
        }

        // Query Subscription Products
        Set<Id> allSubIds = new Set<Id>();
        for (List<KGRenewal__Subscription__c> subs : subsByAccount.values()) {
            for (KGRenewal__Subscription__c s : subs) allSubIds.add(s.Id);
        }
        Map<Id, List<KGRenewal__SubscriptionProduct__c>> subProductsBySub = new Map<Id, List<KGRenewal__SubscriptionProduct__c>>();
        if (!allSubIds.isEmpty()) {
            for (KGRenewal__SubscriptionProduct__c sp : [
                SELECT KGRenewal__Subscription__c, KGRenewal__TotalPrice__c, KGRenewal__Term__c
                FROM KGRenewal__SubscriptionProduct__c
                WHERE KGRenewal__Subscription__c IN :allSubIds
                  AND KGRenewal__IsCurrent__c = true
            ]) {
                if (!subProductsBySub.containsKey(sp.KGRenewal__Subscription__c)) {
                    subProductsBySub.put(sp.KGRenewal__Subscription__c, new List<KGRenewal__SubscriptionProduct__c>());
                }
                subProductsBySub.get(sp.KGRenewal__Subscription__c).add(sp);
            }
        }

        // --- Bulkify open renewals calculation ---
        // Collect all renewal opportunity Ids from all subscriptions
        Set<Id> allRenewalOppIds = new Set<Id>();
        for (List<KGRenewal__Subscription__c> subs : subsByAccount.values()) {
            for (KGRenewal__Subscription__c s : subs) {
                if (s.KGRenewal__RenewalOpportunity__c != null) {
                    allRenewalOppIds.add(s.KGRenewal__RenewalOpportunity__c);
                }
            }
        }
        // Query all renewal opportunities at once
        Map<Id, Opportunity> renewalOppsById = new Map<Id, Opportunity>();
        if (!allRenewalOppIds.isEmpty()) {
            for (Opportunity opp : [SELECT Id, Name, IsClosed FROM Opportunity WHERE Id IN :allRenewalOppIds]) {
                renewalOppsById.put(opp.Id, opp);
            }
        }

        // Query all subscriptions (including inactive) for all accounts - BULKIFIED
        Map<Id, List<KGRenewal__Subscription__c>> allSubsByAccount = new Map<Id, List<KGRenewal__Subscription__c>>();
        for (KGRenewal__Subscription__c sub : [
            SELECT Id, KGRenewal__Account__c
            FROM KGRenewal__Subscription__c
            WHERE KGRenewal__Account__c IN :accountIds
        ]) {
            if (!allSubsByAccount.containsKey(sub.KGRenewal__Account__c)) {
                allSubsByAccount.put(sub.KGRenewal__Account__c, new List<KGRenewal__Subscription__c>());
            }
            allSubsByAccount.get(sub.KGRenewal__Account__c).add(sub);
        }

        // Calculate metrics for each Account
        for (Id accId : accountIds) {
            Map<String, Object> result = new Map<String, Object>();
            Account acc = accounts.get(accId);
            
            // Always add an entry for each requested account ID, even if account doesn't exist
            if (acc == null) {
                // Account doesn't exist - return empty metrics
                result.put('account', null);
                result.put('tenantId', null);
                result.put('subscriptionCount', 0);
                result.put('totalRevenue', 0);
                result.put('totalRenewalAmount', 0);
                result.put('totalAmountPaid', 0);
                result.put('totalAmountRemaining', 0);
                result.put('renewalDate', null);
                result.put('averageSubscriptionValue', 0);
                result.put('opportunityCount', 0);
                result.put('closedWonACV', 0);
                result.put('closedWonCount', 0);
                result.put('annualContractValue', 0);
                result.put('wonValue', 0);
                result.put('lostValue', 0);
                result.put('negotiationValue', 0);
                result.put('lostCount', 0);
                result.put('openOpportunities', 0);
                result.put('renewalCount', 0);
                result.put('openRenewals', 0);
                result.put('proformaInvoiceValue', 0);
                result.put('proformaInvoiceCount', 0);
                result.put('targetedConstructionVolumeTCV', 0);
                result.put('contractEffectiveDate', null);
                result.put('contractEndDate', null);
                result.put('mrr', 0);
                result.put('arr', 0);
                results.put(accId, result);
                continue;
            }
            
            result.put('account', acc);
            result.put('tenantId', acc.Tenant_ID__c);

            List<KGRenewal__Subscription__c> subs = subsByAccount.containsKey(accId) ? subsByAccount.get(accId) : new List<KGRenewal__Subscription__c>();
            // Get all (inactive) subscriptions for this account from bulkified query
            List<KGRenewal__Subscription__c> allSubs = allSubsByAccount.containsKey(accId) ? allSubsByAccount.get(accId) : new List<KGRenewal__Subscription__c>();
            result.put('subscriptionCount', subs.size());

            Decimal totalRevenue = 0;
            Decimal totalRenewalAmount = 0;
            Decimal totalAmountPaid = 0;
            Decimal totalAmountRemaining = 0;
            Date lastRenewalDate;
            for (KGRenewal__Subscription__c s : subs) {
                totalRevenue += (s.KGRenewal__TotalAmount__c != null ? s.KGRenewal__TotalAmount__c : 0);
                totalRenewalAmount += (s.KGRenewal__RenewalAmount__c != null ? s.KGRenewal__RenewalAmount__c : 0);
                totalAmountPaid += (s.KGRenewal__AmountPaid__c != null ? s.KGRenewal__AmountPaid__c : 0);
                totalAmountRemaining += (s.KGRenewal__AmountRemaining__c != null ? s.KGRenewal__AmountRemaining__c : 0);
                if (lastRenewalDate == null || (s.KGRenewal__RenewalDate__c != null && s.KGRenewal__RenewalDate__c > lastRenewalDate)) {
                    lastRenewalDate = s.KGRenewal__RenewalDate__c;
                }
            }
            result.put('totalRevenue', totalRevenue);
            result.put('totalRenewalAmount', totalRenewalAmount);
            result.put('totalAmountPaid', totalAmountPaid);
            result.put('totalAmountRemaining', totalAmountRemaining);
            result.put('renewalDate', lastRenewalDate);
            result.put('averageSubscriptionValue', subs.size() > 0 ? totalRevenue / subs.size() : 0);

            List<Opportunity> opps = oppsByAccount.containsKey(accId) ? oppsByAccount.get(accId) : new List<Opportunity>();
            result.put('opportunityCount', opps.size());

            Decimal won = 0;
            Decimal lost = 0;
            Decimal negotiation = 0;
            Integer lostCount = 0;
            Integer openCount = 0;
            Decimal totalACV = 0;
            Date today = Date.today();

            for (Opportunity o : opps) {
                Boolean isActive = false;
                if (o.KGRenewal__ContractEffectiveDate__c != null && o.KGRenewal__ContractEndDate__c != null) {
                    if (o.KGRenewal__ContractEffectiveDate__c <= today && o.KGRenewal__ContractEndDate__c >= today) {
                        isActive = true;
                    }
                }
                Decimal termYears;
                Decimal acv = 0;
                if (isActive && (o.StageName == 'Closed Won' || o.StageName == 'Proforma Invoice')) {
                    if (o.KGRenewal__Term__c != null && o.KGRenewal__Term__c > 0) {
                        if (o.KGRenewal__Term__c == 12) {
                            acv = (o.Amount != null ? o.Amount : 0);
                        } else {
                            termYears = o.KGRenewal__Term__c / 12;
                            if (o.Amount != null && termYears > 0) {
                                acv = o.Amount / termYears;
                            }
                        }
                    } else if (o.KGRenewal__ContractEffectiveDate__c != null && o.KGRenewal__ContractEndDate__c != null && o.KGRenewal__ContractEndDate__c > o.KGRenewal__ContractEffectiveDate__c) {
                        termYears = ((Decimal) o.KGRenewal__ContractEndDate__c.daysBetween(o.KGRenewal__ContractEffectiveDate__c)) / 365;
                        if (o.Amount != null && termYears > 0) {
                            acv = o.Amount / termYears;
                        }
                    }
                    totalACV += acv;
                }

                if (o.StageName == 'Closed Won') {
                    won += (o.Amount != null ? o.Amount : 0);
                } else if (o.StageName == 'Closed Lost') {
                    lost += (o.Amount != null ? o.Amount : 0);
                    lostCount++;
                } else {
                    negotiation += (o.Amount != null ? o.Amount : 0);
                    openCount++;
                }
            }

            result.put('annualContractValue', totalACV.setScale(2));
            result.put('wonValue', won);
            result.put('lostValue', lost);
            result.put('negotiationValue', negotiation);
            result.put('lostCount', lostCount);
            result.put('openOpportunities', openCount);

            // --- Calculate TCV, Closed Won ACV, Contract Dates ---
            Decimal tcv = 0;
            Decimal closedWonACV = 0;
            Decimal proformaInvoiceValue = 0;
            Integer proformaInvoiceCount = 0;
            Date contractEffectiveDate = null;
            Date contractEndDate = null;
            Boolean hasOpportunities = !opps.isEmpty();
            // Find latest closed won opp for TCV and contract dates
            Opportunity latestClosedWon = null;
            Opportunity latestOpp = null;
            for (Opportunity o : opps) {
                if (o.StageName == 'Closed Won') {
                    if (latestClosedWon == null || (o.CloseDate != null && o.CloseDate > latestClosedWon.CloseDate)) {
                        latestClosedWon = o;
                    }
                }
                if (o.StageName == 'Closed Won' || o.StageName == 'Proforma Invoice') {
                    if (latestOpp == null || (o.CloseDate != null && o.CloseDate > latestOpp.CloseDate)) {
                        latestOpp = o;
                    }
                }
                if (o.StageName == 'Proforma Invoice') {
                    if (o.Amount != null) {
                        proformaInvoiceValue += o.Amount;
                    }
                    proformaInvoiceCount++;
                }
            }
            if (latestClosedWon != null) {
                tcv = latestClosedWon.Targeted_Construction_Volume_TCV__c != null ? latestClosedWon.Targeted_Construction_Volume_TCV__c : 0;
            }
            if (latestOpp != null) {
                contractEffectiveDate = latestOpp.KGRenewal__ContractEffectiveDate__c;
                contractEndDate = latestOpp.KGRenewal__ContractEndDate__c;
                // Closed Won ACV only for closed won
                if (latestOpp.StageName == 'Closed Won') {
                    if (latestOpp.KGRenewal__Term__c != null && latestOpp.KGRenewal__Term__c > 0) {
                        if (latestOpp.KGRenewal__Term__c == 12) {
                            closedWonACV = (latestOpp.Amount != null ? latestOpp.Amount : 0);
                        } else {
                            Decimal termYears = latestOpp.KGRenewal__Term__c / 12;
                            if (latestOpp.Amount != null && termYears > 0) {
                                closedWonACV = latestOpp.Amount / termYears;
                            }
                        }
                    } else if (latestOpp.KGRenewal__ContractEffectiveDate__c != null && latestOpp.KGRenewal__ContractEndDate__c != null && latestOpp.KGRenewal__ContractEndDate__c > latestOpp.KGRenewal__ContractEffectiveDate__c) {
                        Decimal termYears = ((Decimal) latestOpp.KGRenewal__ContractEndDate__c.daysBetween(latestOpp.KGRenewal__ContractEffectiveDate__c)) / 365;
                        if (latestOpp.Amount != null && termYears > 0) {
                            closedWonACV = latestOpp.Amount / termYears;
                        }
                    }
                }
            }
            // Always return metrics, even if empty (don't return null)
            // This ensures consistent processing in the batch
            result.put('targetedConstructionVolumeTCV', tcv);
            result.put('closedWonACV', closedWonACV);
            result.put('contractEffectiveDate', contractEffectiveDate);
            result.put('contractEndDate', contractEndDate);
            result.put('proformaInvoiceValue', proformaInvoiceValue);
            result.put('proformaInvoiceCount', proformaInvoiceCount);

            // Calculate ARR and MRR from Subscription Products
            List<Id> activeSubIds = new List<Id>();
            for (KGRenewal__Subscription__c s : subs) {
                activeSubIds.add(s.Id);
            }
            Decimal totalMRR = 0;
            if (!activeSubIds.isEmpty()) {
                for (Id subId : activeSubIds) {
                    List<KGRenewal__SubscriptionProduct__c> subProducts = subProductsBySub.containsKey(subId) ? subProductsBySub.get(subId) : new List<KGRenewal__SubscriptionProduct__c>();
                    for (KGRenewal__SubscriptionProduct__c sp : subProducts) {
                        if (sp.KGRenewal__TotalPrice__c != null && sp.KGRenewal__Term__c != null && sp.KGRenewal__Term__c > 0) {
                            totalMRR += sp.KGRenewal__TotalPrice__c / sp.KGRenewal__Term__c;
                        }
                    }
                }
            }
            Decimal totalARR = totalMRR * 12;
            result.put('mrr', totalMRR.setScale(2));
            result.put('arr', totalARR.setScale(2));

            // Calculate open renewals for this account
            Integer openRenewals = 0;
            for (KGRenewal__Subscription__c s : subs) {
                if (s.KGRenewal__RenewalOpportunity__c != null) {
                    Opportunity renewalOpp = renewalOppsById.get(s.KGRenewal__RenewalOpportunity__c);
                    if (renewalOpp != null && !renewalOpp.IsClosed) {
                        openRenewals++;
                    }
                }
            }
            result.put('openRenewals', openRenewals);

            // Add any missing metrics that might be referenced in the batch
            if (!result.containsKey('renewalCount')) {
                result.put('renewalCount', subs.size()); // Count of active subscriptions as renewal count
            }
            if (!result.containsKey('closedWonCount')) {
                Integer closedWonCount = 0;
                for (Opportunity o : opps) {
                    if (o.StageName == 'Closed Won') {
                        closedWonCount++;
                    }
                }
                result.put('closedWonCount', closedWonCount);
            }

            results.put(accId, result);
        }
        return results;
    }
}