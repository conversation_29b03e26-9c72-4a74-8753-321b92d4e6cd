import { LightningElement, api, wire } from 'lwc';
import getClientSalesData from '@salesforce/apex/ClientSalesController.getClientSalesData';
import getClientType from '@salesforce/apex/ClientSalesController.getClientType';
import { getRecord } from 'lightning/uiRecordApi';
import SHEETJS from '@salesforce/resourceUrl/SheetJS';
import SNAPCOUNT_LOGO from '@salesforce/resourceUrl/snapcountapplogo';
import exportActiveSubscriptions from '@salesforce/apex/ClientSalesController.exportActiveSubscriptions';
import exportActiveMRR from '@salesforce/apex/ClientSalesController.exportActiveMRR';
import exportActiveARR from '@salesforce/apex/ClientSalesController.exportActiveARR';
import exportActiveACVOpportunities from '@salesforce/apex/ClientSalesController.exportActiveACVOpportunities';
import exportTotalRevenue from '@salesforce/apex/ClientSalesController.exportTotalRevenue';
import exportRenewalAmount from '@salesforce/apex/ClientSalesController.exportRenewalAmount';
import exportAmountPaid from '@salesforce/apex/ClientSalesController.exportAmountPaid';
import exportAmountRemaining from '@salesforce/apex/ClientSalesController.exportAmountRemaining';
import exportOpenRenewals from '@salesforce/apex/ClientSalesController.exportOpenRenewals';
import exportTotalOpportunities from '@salesforce/apex/ClientSalesController.exportTotalOpportunities';
import exportOpenOpportunities from '@salesforce/apex/ClientSalesController.exportOpenOpportunities';
import exportWonValue from '@salesforce/apex/ClientSalesController.exportWonValue';
import exportNegotiationValue from '@salesforce/apex/ClientSalesController.exportNegotiationValue';
import exportLost from '@salesforce/apex/ClientSalesController.exportLost';
import exportTotalTenantId from '@salesforce/apex/ClientSalesController.exportTotalTenantId';
import exportProformaInvoice from '@salesforce/apex/ClientSalesController.exportProformaInvoice';
import exportClosedWonACV from '@salesforce/apex/ClientSalesController.exportClosedWonACV';

export default class ClientSalesOverview extends LightningElement {
  @api recordId;
  accountId;
  tenantId;
  clientType = 'Standard Client'; // Default value
  snapcountLogoUrl = SNAPCOUNT_LOGO;

  subscriptionCount = 0;
  totalRevenue = 0;
  totalRenewalAmount = 0;
  totalAmountPaid = 0;
  totalAmountRemaining = 0;
  renewalDate;
  opportunityCount = 0;
  openOpportunities = 0;
  wonValue = 0;
  lostValue = 0;
  lostCount = 0;
  negotiationValue = 0;
  annualContractValue = 0;
  mrr = 0;
  arr = 0;
  proformaInvoiceCount = 0;
  proformaInvoiceValue = 0;
  closedWonCount = 0;
  closedWonACV = 0;
  openRenewals = 0;
  sheetJsInitialized = false;

  helpText = {
    subscriptionCount: 'Number of active subscriptions for this client.',
    annualContractValue: 'Sum of annualized contract values for currently active opportunities (ACV = Amount / Term in years, only for active contracts).',
    mrr: 'Monthly Recurring Revenue: Sum of (Total Price / Term in months) for all current subscription products.',
    arr: 'Annual Recurring Revenue: MRR multiplied by 12.',
    totalRevenue: 'Sum of Total Amounts from all active subscriptions.',
    totalRenewalAmount: 'Sum of Renewal Amounts from all active subscriptions.',
    totalAmountPaid: 'Sum of Amount Paid from all active subscriptions.',
    totalAmountRemaining: 'Sum of Amount Remaining from all active subscriptions.',
    renewalDate: 'Most recent renewal date among all active subscriptions.',
    opportunityCount: 'Total number of opportunities for this client.',
    openOpportunities: 'Number of open opportunities (not closed won/lost).',
    wonValue: 'Sum of Amounts for Closed Won opportunities.',
    lostValue: 'Sum of Amounts for Closed Lost opportunities.',
    lostCount: 'Number of Closed Lost opportunities.',
    negotiationValue: 'Sum of Amounts for opportunities in negotiation (not closed).'
  };

  @wire(getRecord, { recordId: '$recordId', fields: ['Account.Id'] })
  wiredAccount({ data }) {
    if (data) {
      this.accountId = data.fields.Id.value;
      this.loadClientData();
    }
  }

  async loadClientData() {
    try {
      const result = await getClientSalesData({ accountId: this.accountId });
      Object.assign(this, result);
      
      // Load client type
      await this.loadClientType();
    } catch (err) {
      console.error('Error loading client data', err);
    }
  }

  async loadClientType() {
    try {
      this.clientType = await getClientType({ accountId: this.accountId });
    } catch (err) {
      console.error('Error loading client type', err);
      this.clientType = 'Standard Client'; // Fallback
    }
  }

  get formattedSubscriptionCount() {
    return this.subscriptionCount.toLocaleString('en-US');
  }
  get formattedTotalRevenue() {
    return this.totalRevenue.toLocaleString('en-US', { minimumFractionDigits: 2 });
  }
  get formattedTotalRenewalAmount() {
    return this.totalRenewalAmount.toLocaleString('en-US', { minimumFractionDigits: 2 });
  }
  get formattedTotalAmountPaid() {
    return this.totalAmountPaid.toLocaleString('en-US', { minimumFractionDigits: 2 });
  }
  get formattedTotalAmountRemaining() {
    return this.totalAmountRemaining.toLocaleString('en-US', { minimumFractionDigits: 2 });
  }
  get formattedWonValue() {
    return this.wonValue.toLocaleString('en-US', { minimumFractionDigits: 2 });
  }
  get formattedLostValue() {
    return this.lostValue.toLocaleString('en-US', { minimumFractionDigits: 2 });
  }
  get formattedNegotiationValue() {
    return this.negotiationValue.toLocaleString('en-US', { minimumFractionDigits: 2 });
  }
  get formattedAnnualContractValue() {
    return this.annualContractValue.toLocaleString('en-US', { minimumFractionDigits: 2 });
  }
  get formattedTenantId() {
    return this.tenantId != null ? this.tenantId.toLocaleString('en-US', { maximumFractionDigits: 0 }) : '';
  }
  get formattedMRR() {
    return this.mrr.toLocaleString('en-US', { minimumFractionDigits: 2 });
  }
  get formattedARR() {
    return this.arr.toLocaleString('en-US', { minimumFractionDigits: 2 });
  }
  get formattedOpenRenewals() {
    return Number(this.openRenewals).toLocaleString('en-US');
  }
  get formattedRenewalAmount() {
    return this.renewalAmount ? Number(this.renewalAmount).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) : '0.00';
  }
  get formattedClosedWonACV() {
    return this.closedWonACV ? `$ ${Number(this.closedWonACV).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}` : '$ 0.00';
  }

  get clientTypeClass() {
    return this.clientType === 'Unlimited Client' ? 'unlimited' : 'standard';
  }

  get clientTypeIcon() {
    return this.snapcountLogoUrl;
  }

  get isUnlimitedClient() {
    return this.clientType === 'Unlimited Client';
  }

  get showLogo() {
    return true; // Always show logo instead of icon
  }

  getHelpText(key) {
    let text = this.helpText[key] || '';
    return text.replace(/["'<>&]/g, (char) => ({
      '"': '&quot;',
      "'": '&#39;',
      '<': '&lt;',
      '>': '&gt;',
      '&': '&amp;'
    })[char]);
  }

  get subscriptionCountHelp() { return this.getHelpText('subscriptionCount'); }
  get annualContractValueHelp() { return this.getHelpText('annualContractValue'); }
  get mrrHelp() { return this.getHelpText('mrr'); }
  get arrHelp() { return this.getHelpText('arr'); }
  get totalRevenueHelp() { return this.getHelpText('totalRevenue'); }
  get totalRenewalAmountHelp() { return this.getHelpText('totalRenewalAmount'); }
  get totalAmountPaidHelp() { return this.getHelpText('totalAmountPaid'); }
  get totalAmountRemainingHelp() { return this.getHelpText('totalAmountRemaining'); }
  get renewalDateHelp() { return this.getHelpText('renewalDate'); }
  get opportunityCountHelp() { return this.getHelpText('opportunityCount'); }
  get openOpportunitiesHelp() { return this.getHelpText('openOpportunities'); }
  get wonValueHelp() { return this.getHelpText('wonValue'); }
  get negotiationValueHelp() { return this.getHelpText('negotiationValue'); }
  get lostCountHelp() { return this.getHelpText('lostCount'); }

  renderedCallback() {
    if (this.sheetJsInitialized) return;
    this.sheetJsInitialized = true;
    const script = document.createElement('script');
    script.src = SHEETJS;
    script.onload = () => {};
    script.async = true;
    document.body.appendChild(script);
  }

  // --- Export Handlers ---
  async handleExportActiveSubscriptions() {
    const data = await exportActiveSubscriptions({ accountId: this.accountId });
    this.exportToXlsx(data, 'ActiveSubscriptions.xlsx');
  }
  async handleExportMRR() {
    const data = await exportActiveMRR({ accountId: this.accountId });
    this.exportToXlsx(data, 'ActiveMRR.xlsx');
  }
  async handleExportARR() {
    const data = await exportActiveARR({ accountId: this.accountId });
    this.exportToXlsx(data, 'ActiveARR.xlsx');
  }
  async handleExportTotalRevenue() {
    const data = await exportTotalRevenue({ accountId: this.accountId });
    this.exportToXlsx(data, 'TotalClosedWon.xlsx');
  }
  async handleExportRenewalAmount() {
    const data = await exportRenewalAmount({ accountId: this.accountId });
    this.exportToXlsx(data, 'RenewalAmount.xlsx');
  }
  async handleExportAmountPaid() {
    const data = await exportAmountPaid({ accountId: this.accountId });
    this.exportToXlsx(data, 'AmountPaid.xlsx');
  }
  async handleExportAmountRemaining() {
    const data = await exportAmountRemaining({ accountId: this.accountId });
    this.exportToXlsx(data, 'AmountRemaining.xlsx');
  }
  async handleExportOpenRenewals() {
    const data = await exportOpenRenewals({ accountId: this.accountId });
    this.exportToXlsx(data, 'OpenRenewals.xlsx');
  }
  async handleExportTotalOpportunities() {
    const data = await exportTotalOpportunities({ accountId: this.accountId });
    this.exportToXlsx(data, 'TotalOpportunities.xlsx');
  }
  async handleExportOpenOpportunities() {
    const data = await exportOpenOpportunities({ accountId: this.accountId });
    this.exportToXlsx(data, 'OpenOpportunities.xlsx');
  }
  async handleExportWonValue() {
    const data = await exportWonValue({ accountId: this.accountId });
    this.exportToXlsx(data, 'WonValue.xlsx');
  }
  async handleExportNegotiationValue() {
    const data = await exportNegotiationValue({ accountId: this.accountId });
    this.exportToXlsx(data, 'NegotiationValue.xlsx');
  }
  async handleExportLost() {
    const data = await exportLost({ accountId: this.accountId });
    this.exportToXlsx(data, 'Lost.xlsx');
  }
  async handleExportProformaInvoice() {
    const data = await exportProformaInvoice({ accountId: this.accountId });
    this.exportToXlsx(data, 'ProformaInvoiceACV.xlsx');
  }
  async handleExportClosedWonACV() {
    const data = await exportClosedWonACV({ accountId: this.accountId });
    this.exportToXlsx(data, 'ClosedWonACV.xlsx');
  }
  async handleExportACV() {
    const data = await exportActiveACVOpportunities({ accountId: this.accountId });
    this.exportToXlsx(data, 'ActiveACV.xlsx');
  }

  exportToXlsx(data, filename) {
    if (!window.XLSX) {
      // SheetJS not loaded
      alert('SheetJS library not loaded.');
      return;
    }
    const ws = window.XLSX.utils.json_to_sheet(data);
    const wb = window.XLSX.utils.book_new();
    window.XLSX.utils.book_append_sheet(wb, ws, 'Data');
    window.XLSX.writeFile(wb, filename);
  }
}