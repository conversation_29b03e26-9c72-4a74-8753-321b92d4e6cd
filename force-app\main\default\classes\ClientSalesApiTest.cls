@isTest
private class ClientSalesApiTest {
    
    @testSetup
    static void setupTestData() {
        // Create test account
        Account testAccount = new Account(
            Name = 'Test API Account',
            Tenant_ID__c = 1002
        );
        insert testAccount;
        
        // Create test opportunities
        Date today = Date.today();
        
        Opportunity testOpp = new Opportunity(
            Name = 'Test API Opportunity',
            AccountId = testAccount.Id,
            Amount = 120000,
            StageName = 'Closed Won',
            CloseDate = today.addDays(-30),
            KGRenewal__ContractEffectiveDate__c = today.addDays(-30),
            KGRenewal__ContractEndDate__c = today.addDays(330),
            KGRenewal__Term__c = 12
        );
        insert testOpp;
        
        // Create test subscription
        KGRenewal__Subscription__c testSub = new KGRenewal__Subscription__c(
            KGRenewal__Account__c = testAccount.Id,
            KGRenewal__TotalAmount__c = 100000,
            KGRenewal__RenewalAmount__c = 90000,
            KGRenewal__AmountPaid__c = 60000,
            <PERSON><PERSON><PERSON>wal__StartDate__c = today.addDays(-100),
            K<PERSON><PERSON>wal__EndDate__c = today.addDays(265),
            K<PERSON>enewal__RenewalDate__c = today.addDays(200)
        );
        insert testSub;
        
        // Create test subscription product
        KGRenewal__SubscriptionProduct__c testSubProduct = new KGRenewal__SubscriptionProduct__c(
            Name = 'Test API Product',
            KGRenewal__Subscription__c = testSub.Id,
            KGRenewal__TotalPrice__c = 50000,
            KGRenewal__Term__c = 12,
            KGRenewal__Quantity__c = 1,
            KGRenewal__UnitPrice__c = 50000
        );
        insert testSubProduct;
    }
    
    @isTest
    static void testGetAllClientSalesDataWithoutFields() {
        RestRequest req = new RestRequest();
        RestResponse res = new RestResponse();
        
        req.requestURI = '/services/apexrest/client-sales-summary';
        req.httpMethod = 'GET';
        
        RestContext.request = req;
        RestContext.response = res;
        
        Test.startTest();
        ClientSalesApi.getAllClientSalesData();
        Test.stopTest();
        
        System.assertEquals(200, res.statusCode, 'Should return 200 status code');
        System.assertNotEquals(null, res.responseBody, 'Response body should not be null');
        
        // Parse response to verify structure
        String responseString = res.responseBody.toString();
        System.assert(responseString.contains('accountId'), 'Response should contain accountId');
        System.assert(responseString.contains('totalRevenue'), 'Response should contain totalRevenue');
        System.assert(responseString.contains('mrr'), 'Response should contain mrr');
        System.assert(responseString.contains('arr'), 'Response should contain arr');
    }
    
    @isTest
    static void testGetAllClientSalesDataWithFields() {
        RestRequest req = new RestRequest();
        RestResponse res = new RestResponse();
        
        req.requestURI = '/services/apexrest/client-sales-summary?fields=accountId,accountName,totalRevenue,mrr,arr';
        req.httpMethod = 'GET';
        
        RestContext.request = req;
        RestContext.response = res;
        
        Test.startTest();
        ClientSalesApi.getAllClientSalesData();
        Test.stopTest();
        
        System.assertEquals(200, res.statusCode, 'Should return 200 status code');
        System.assertNotEquals(null, res.responseBody, 'Response body should not be null');
        
        // Parse response to verify filtered fields
        String responseString = res.responseBody.toString();
        System.assert(responseString.contains('accountId'), 'Response should contain accountId');
        System.assert(responseString.contains('totalRevenue'), 'Response should contain totalRevenue');
        System.assert(responseString.contains('mrr'), 'Response should contain mrr');
        System.assert(responseString.contains('arr'), 'Response should contain arr');
    }
    
    @isTest
    static void testGetAllClientSalesDataWithEmptyFields() {
        RestRequest req = new RestRequest();
        RestResponse res = new RestResponse();
        
        req.requestURI = '/services/apexrest/client-sales-summary?fields=';
        req.httpMethod = 'GET';
        
        RestContext.request = req;
        RestContext.response = res;
        
        Test.startTest();
        ClientSalesApi.getAllClientSalesData();
        Test.stopTest();
        
        System.assertEquals(200, res.statusCode, 'Should return 200 status code');
        System.assertNotEquals(null, res.responseBody, 'Response body should not be null');
    }
    
    @isTest
    static void testApiErrorHandling() {
        // Test error handling by creating a scenario that might cause issues
        RestRequest req = new RestRequest();
        RestResponse res = new RestResponse();
        
        req.requestURI = '/services/apexrest/client-sales-summary';
        req.httpMethod = 'GET';
        
        RestContext.request = req;
        RestContext.response = res;
        
        Test.startTest();
        
        // Delete all test data to create potential null pointer scenarios
        delete [SELECT Id FROM KGRenewal__SubscriptionProduct__c];
        delete [SELECT Id FROM KGRenewal__Subscription__c];
        delete [SELECT Id FROM Opportunity];
        delete [SELECT Id FROM Account];
        
        ClientSalesApi.getAllClientSalesData();
        Test.stopTest();
        
        // Should handle empty data gracefully
        System.assertNotEquals(null, res.responseBody, 'Response body should not be null even with no data');
    }
    
    @isTest
    static void testApiWithMultipleAccounts() {
        // Create additional test data
        List<Account> additionalAccounts = new List<Account>();
        for (Integer i = 0; i < 3; i++) {
            additionalAccounts.add(new Account(
                Name = 'Additional Account ' + i,
                Tenant_ID__c = 3000 + i
            ));
        }
        insert additionalAccounts;
        
        // Create opportunities for additional accounts
        List<Opportunity> additionalOpps = new List<Opportunity>();
        Date today = Date.today();
        
        for (Account acc : additionalAccounts) {
            additionalOpps.add(new Opportunity(
                Name = 'Additional Opp for ' + acc.Name,
                AccountId = acc.Id,
                Amount = 50000,
                StageName = 'Closed Won',
                CloseDate = today.addDays(-15),
                KGRenewal__ContractEffectiveDate__c = today.addDays(-15),
                KGRenewal__ContractEndDate__c = today.addDays(350),
                KGRenewal__Term__c = 12
            ));
        }
        insert additionalOpps;
        
        // Create subscriptions for additional accounts to make them active
        List<KGRenewal__Subscription__c> additionalSubs = new List<KGRenewal__Subscription__c>();
        for (Account acc : additionalAccounts) {
            additionalSubs.add(new KGRenewal__Subscription__c(
                KGRenewal__Account__c = acc.Id,
                KGRenewal__TotalAmount__c = 40000,
                KGRenewal__RenewalAmount__c = 35000,
                KGRenewal__AmountPaid__c = 20000,
                KGRenewal__StartDate__c = today.addDays(-60),
                KGRenewal__EndDate__c = today.addDays(305),
                KGRenewal__RenewalDate__c = today.addDays(240)
            ));
        }
        insert additionalSubs;
        
        // Create subscription products for additional accounts
        List<KGRenewal__SubscriptionProduct__c> additionalSubProducts = new List<KGRenewal__SubscriptionProduct__c>();
        for (Integer i = 0; i < additionalSubs.size(); i++) {
            additionalSubProducts.add(new KGRenewal__SubscriptionProduct__c(
                Name = 'Additional Product ' + i,
                KGRenewal__Subscription__c = additionalSubs[i].Id,
                KGRenewal__TotalPrice__c = 20000,
                KGRenewal__Term__c = 12,
                KGRenewal__Quantity__c = 1,
                KGRenewal__UnitPrice__c = 20000
            ));
        }
        insert additionalSubProducts;
        
        RestRequest req = new RestRequest();
        RestResponse res = new RestResponse();
        
        req.requestURI = '/services/apexrest/client-sales-summary';
        req.httpMethod = 'GET';
        
        RestContext.request = req;
        RestContext.response = res;
        
        Test.startTest();
        ClientSalesApi.getAllClientSalesData();
        Test.stopTest();
        
        System.assertEquals(200, res.statusCode, 'Should return 200 status code');
        System.assertNotEquals(null, res.responseBody, 'Response body should not be null');
        
        // Verify response contains data for multiple accounts
        String responseString = res.responseBody.toString();
        System.assert(responseString.contains('Additional Account'), 'Response should contain additional accounts');
    }
}