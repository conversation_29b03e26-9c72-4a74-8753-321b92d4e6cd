global without sharing class UpdateAccountSalesMetricsBatch implements Database.Batchable<SObject> {
    public String operationId;
    private List<K<PERSON>enewal__KineticGrowthLog__c> pendingLogs = new List<KGRenewal__KineticGrowthLog__c>();

    global UpdateAccountSalesMetricsBatch() {
        // Create shorter operation ID to fit in 18 character limit
        this.operationId = 'B' + String.valueOf(System.currentTimeMillis()).right(8) + System.now().format('MMddHHmm');
    }

    public String getOperationId() {
        return this.operationId;
    }
    
    global Database.QueryLocator start(Database.BatchableContext bc) {
        // Log batch start
        logEvent('Batch Started', 'Starting UpdateAccountSalesMetricsBatch with operation ID: ' + operationId, true);
        
        // Return empty query locator if context is null
        if (bc == null) {
            logEvent('Batch Error', 'BatchableContext is null', false);
            flushLogs();
            return Database.getQueryLocator([SELECT Id FROM Account WHERE Id = null]);
        }

        // Return empty query locator if there are no opportunities
        List<Opportunity> opps = [SELECT Id FROM Opportunity LIMIT 1];
        if (opps.isEmpty()) {
            logEvent('Batch Info', 'No opportunities found, skipping batch execution', true);
            flushLogs();
            return Database.getQueryLocator([SELECT Id FROM Account WHERE Id = null]);
        }
        
        logEvent('Batch Info', 'Query locator created for accounts', true);

        // Flush logs from start method to ensure they're committed
        flushLogs();

        return Database.getQueryLocator([SELECT Id,
            Active_Subscription_Countc__c, Total_Revenuec__c, Total_Renewal_Amountc__c, Renewal_Amountc__c, Total_Amount_Paidc__c,
            Total_Amount_Remainingc__c, Average_Subscription_Valuec__c, Renewal_Datec__c, Opportunity_Countc__c,
            Open_Opportunitiesc__c, Won_Valuec__c, Lost_Valuec__c, Lost_Countc__c, Negotiation_Valuec__c,
            Annual_Contract_Valuec__c, MRRc__c, ARRc__c, Targeted_Construction_Volume_TCVc__c,
            Contract_Effective_Datec__c, Contract_End_Datec__c, Closed_Won_ACVc__c, Closed_Won_Countc__c,
            Open_Renewalsc__c, Proforma_Invoice_Valuec__c, Proforma_Invoice_Countc__c, Renewal_Countc__c
            FROM Account]);
    }
    
    global void execute(Database.BatchableContext bc, List<Account> scope) {
        logEvent('Processing Account Chunk', 'Processing ' + scope.size() + ' accounts', true);
        
        List<Id> accountIds = new List<Id>();
        Map<Id, Account> oldAccounts = new Map<Id, Account>();
        for (Account acc : scope) {
            accountIds.add(acc.Id);
            oldAccounts.put(acc.Id, acc);
        }
        
        try {
            // Use bulk calculation to avoid SOQL limits
            Map<Id, Map<String, Object>> allMetrics = ClientSalesMetricsUtil.calculateMetrics(accountIds);
            List<Account> updates = new List<Account>();
            Integer processedCount = 0;
            Integer changedCount = 0;
            
            for (Account acc : scope) {
                processedCount++;
                Map<String, Object> metrics = allMetrics.get(acc.Id);
                Account oldAcc = oldAccounts.get(acc.Id);
                Account updateAcc = null;

                if (metrics != null && hasNonZeroMetrics(metrics)) {
                    updateAcc = buildAccountUpdate(acc.Id, metrics, oldAcc);

                    if (updateAcc != null) {
                        updates.add(updateAcc);
                        changedCount++;
                        // Log detailed updates for accounts with significant changes
                        logAccountUpdate(acc.Id, oldAcc, metrics);
                    }
                } else {
                    // Handle accounts with no related data - set all metrics to zero/null
                    updateAcc = buildAccountUpdateForEmptyMetrics(acc.Id, oldAcc);

                    if (updateAcc != null) {
                        updates.add(updateAcc);
                        changedCount++;
                        logEvent('Empty Metrics Update', 'Account ' + acc.Id + ' - updated with empty metrics', true);
                    }
                }

                // Remove per-account logging to avoid DML limits
                // if (updateAcc == null) {
                //     logEvent('No Changes Required', 'Account ' + acc.Id + ' - no field changes detected', true);
                // }
            }
            
            if (!updates.isEmpty()) {
                update updates;
                logEvent('Batch Update Success', 'Processed ' + processedCount + ' accounts, updated ' + changedCount + ' accounts (' + updates.size() + ' records) in chunk', true);
            } else {
                logEvent('Batch Info', 'Processed ' + processedCount + ' accounts, no updates needed in this chunk', true);
            }

        } catch (Exception e) {
            logEvent('Batch Error', 'Error processing chunk: ' + e.getMessage() + ' | Stack: ' + e.getStackTraceString(), false);
            throw e;
        } finally {
            // Flush any remaining logs at the end of each chunk
            flushLogs();
        }
    }
    
    global void finish(Database.BatchableContext bc) {
        logEvent('Batch Completed', 'UpdateAccountSalesMetricsBatch completed successfully', true);
        // Flush any remaining logs at the end of the batch
        flushLogs();
    }
    
    private Account buildAccountUpdate(Id accountId, Map<String, Object> metrics, Account oldAcc) {
        Account updateAcc = new Account(Id = accountId);
        Boolean changed = false;
        List<String> changedFields = new List<String>();
        
        // Check each field for changes
        if (checkAndSetField(updateAcc, oldAcc, metrics, 'Active_Subscription_Countc__c', 'subscriptionCount', changedFields)) changed = true;
        if (checkAndSetField(updateAcc, oldAcc, metrics, 'Total_Revenuec__c', 'totalRevenue', changedFields)) changed = true;
        if (checkAndSetField(updateAcc, oldAcc, metrics, 'Total_Renewal_Amountc__c', 'totalRenewalAmount', changedFields)) changed = true;
        if (checkAndSetField(updateAcc, oldAcc, metrics, 'Renewal_Amountc__c', 'totalRenewalAmount', changedFields)) changed = true;
        if (checkAndSetField(updateAcc, oldAcc, metrics, 'Total_Amount_Paidc__c', 'totalAmountPaid', changedFields)) changed = true;
        if (checkAndSetField(updateAcc, oldAcc, metrics, 'Total_Amount_Remainingc__c', 'totalAmountRemaining', changedFields)) changed = true;
        if (checkAndSetField(updateAcc, oldAcc, metrics, 'Average_Subscription_Valuec__c', 'averageSubscriptionValue', changedFields)) changed = true;
        if (checkAndSetField(updateAcc, oldAcc, metrics, 'Renewal_Datec__c', 'renewalDate', changedFields)) changed = true;
        if (checkAndSetField(updateAcc, oldAcc, metrics, 'Opportunity_Countc__c', 'opportunityCount', changedFields)) changed = true;
        if (checkAndSetField(updateAcc, oldAcc, metrics, 'Open_Opportunitiesc__c', 'openOpportunities', changedFields)) changed = true;
        if (checkAndSetField(updateAcc, oldAcc, metrics, 'Won_Valuec__c', 'wonValue', changedFields)) changed = true;
        if (checkAndSetField(updateAcc, oldAcc, metrics, 'Lost_Valuec__c', 'lostValue', changedFields)) changed = true;
        if (checkAndSetField(updateAcc, oldAcc, metrics, 'Lost_Countc__c', 'lostCount', changedFields)) changed = true;
        if (checkAndSetField(updateAcc, oldAcc, metrics, 'Negotiation_Valuec__c', 'negotiationValue', changedFields)) changed = true;
        if (checkAndSetField(updateAcc, oldAcc, metrics, 'Annual_Contract_Valuec__c', 'annualContractValue', changedFields)) changed = true;
        if (checkAndSetField(updateAcc, oldAcc, metrics, 'MRRc__c', 'mrr', changedFields)) changed = true;
        if (checkAndSetField(updateAcc, oldAcc, metrics, 'ARRc__c', 'arr', changedFields)) changed = true;
        if (checkAndSetField(updateAcc, oldAcc, metrics, 'Targeted_Construction_Volume_TCVc__c', 'targetedConstructionVolumeTCV', changedFields)) changed = true;
        if (checkAndSetField(updateAcc, oldAcc, metrics, 'Contract_Effective_Datec__c', 'contractEffectiveDate', changedFields)) changed = true;
        if (checkAndSetField(updateAcc, oldAcc, metrics, 'Contract_End_Datec__c', 'contractEndDate', changedFields)) changed = true;
        if (checkAndSetField(updateAcc, oldAcc, metrics, 'Closed_Won_ACVc__c', 'closedWonACV', changedFields)) changed = true;
        if (checkAndSetField(updateAcc, oldAcc, metrics, 'Closed_Won_Countc__c', 'closedWonCount', changedFields)) changed = true;
        if (checkAndSetField(updateAcc, oldAcc, metrics, 'Open_Renewalsc__c', 'openRenewals', changedFields)) changed = true;
        if (checkAndSetField(updateAcc, oldAcc, metrics, 'Proforma_Invoice_Valuec__c', 'proformaInvoiceValue', changedFields)) changed = true;
        if (checkAndSetField(updateAcc, oldAcc, metrics, 'Proforma_Invoice_Countc__c', 'proformaInvoiceCount', changedFields)) changed = true;
        if (checkAndSetField(updateAcc, oldAcc, metrics, 'Renewal_Countc__c', 'renewalCount', changedFields)) changed = true;
        
        return changed ? updateAcc : null;
    }

    private Account buildAccountUpdateForEmptyMetrics(Id accountId, Account oldAcc) {
        Account updateAcc = new Account(Id = accountId);
        Boolean changed = false;
        List<String> changedFields = new List<String>();

        // Create empty metrics map
        Map<String, Object> emptyMetrics = new Map<String, Object>{
            'subscriptionCount' => 0,
            'totalRevenue' => 0,
            'totalRenewalAmount' => 0,
            'totalAmountPaid' => 0,
            'totalAmountRemaining' => 0,
            'averageSubscriptionValue' => 0,
            'renewalDate' => null,
            'opportunityCount' => 0,
            'openOpportunities' => 0,
            'wonValue' => 0,
            'lostValue' => 0,
            'lostCount' => 0,
            'negotiationValue' => 0,
            'annualContractValue' => 0,
            'mrr' => 0,
            'arr' => 0,
            'targetedConstructionVolumeTCV' => 0,
            'contractEffectiveDate' => null,
            'contractEndDate' => null,
            'closedWonACV' => 0,
            'closedWonCount' => 0,
            'openRenewals' => 0,
            'proformaInvoiceValue' => 0,
            'proformaInvoiceCount' => 0,
            'renewalCount' => 0
        };

        // Check each field for changes using empty metrics
        if (checkAndSetField(updateAcc, oldAcc, emptyMetrics, 'Active_Subscription_Countc__c', 'subscriptionCount', changedFields)) changed = true;
        if (checkAndSetField(updateAcc, oldAcc, emptyMetrics, 'Total_Revenuec__c', 'totalRevenue', changedFields)) changed = true;
        if (checkAndSetField(updateAcc, oldAcc, emptyMetrics, 'Total_Renewal_Amountc__c', 'totalRenewalAmount', changedFields)) changed = true;
        if (checkAndSetField(updateAcc, oldAcc, emptyMetrics, 'Renewal_Amountc__c', 'totalRenewalAmount', changedFields)) changed = true;
        if (checkAndSetField(updateAcc, oldAcc, emptyMetrics, 'Total_Amount_Paidc__c', 'totalAmountPaid', changedFields)) changed = true;
        if (checkAndSetField(updateAcc, oldAcc, emptyMetrics, 'Total_Amount_Remainingc__c', 'totalAmountRemaining', changedFields)) changed = true;
        if (checkAndSetField(updateAcc, oldAcc, emptyMetrics, 'Average_Subscription_Valuec__c', 'averageSubscriptionValue', changedFields)) changed = true;
        if (checkAndSetField(updateAcc, oldAcc, emptyMetrics, 'Renewal_Datec__c', 'renewalDate', changedFields)) changed = true;
        if (checkAndSetField(updateAcc, oldAcc, emptyMetrics, 'Opportunity_Countc__c', 'opportunityCount', changedFields)) changed = true;
        if (checkAndSetField(updateAcc, oldAcc, emptyMetrics, 'Open_Opportunitiesc__c', 'openOpportunities', changedFields)) changed = true;
        if (checkAndSetField(updateAcc, oldAcc, emptyMetrics, 'Won_Valuec__c', 'wonValue', changedFields)) changed = true;
        if (checkAndSetField(updateAcc, oldAcc, emptyMetrics, 'Lost_Valuec__c', 'lostValue', changedFields)) changed = true;
        if (checkAndSetField(updateAcc, oldAcc, emptyMetrics, 'Lost_Countc__c', 'lostCount', changedFields)) changed = true;
        if (checkAndSetField(updateAcc, oldAcc, emptyMetrics, 'Negotiation_Valuec__c', 'negotiationValue', changedFields)) changed = true;
        if (checkAndSetField(updateAcc, oldAcc, emptyMetrics, 'Annual_Contract_Valuec__c', 'annualContractValue', changedFields)) changed = true;
        if (checkAndSetField(updateAcc, oldAcc, emptyMetrics, 'MRRc__c', 'mrr', changedFields)) changed = true;
        if (checkAndSetField(updateAcc, oldAcc, emptyMetrics, 'ARRc__c', 'arr', changedFields)) changed = true;
        if (checkAndSetField(updateAcc, oldAcc, emptyMetrics, 'Targeted_Construction_Volume_TCVc__c', 'targetedConstructionVolumeTCV', changedFields)) changed = true;
        if (checkAndSetField(updateAcc, oldAcc, emptyMetrics, 'Contract_Effective_Datec__c', 'contractEffectiveDate', changedFields)) changed = true;
        if (checkAndSetField(updateAcc, oldAcc, emptyMetrics, 'Contract_End_Datec__c', 'contractEndDate', changedFields)) changed = true;
        if (checkAndSetField(updateAcc, oldAcc, emptyMetrics, 'Closed_Won_ACVc__c', 'closedWonACV', changedFields)) changed = true;
        if (checkAndSetField(updateAcc, oldAcc, emptyMetrics, 'Closed_Won_Countc__c', 'closedWonCount', changedFields)) changed = true;
        if (checkAndSetField(updateAcc, oldAcc, emptyMetrics, 'Open_Renewalsc__c', 'openRenewals', changedFields)) changed = true;
        if (checkAndSetField(updateAcc, oldAcc, emptyMetrics, 'Proforma_Invoice_Valuec__c', 'proformaInvoiceValue', changedFields)) changed = true;
        if (checkAndSetField(updateAcc, oldAcc, emptyMetrics, 'Proforma_Invoice_Countc__c', 'proformaInvoiceCount', changedFields)) changed = true;
        if (checkAndSetField(updateAcc, oldAcc, emptyMetrics, 'Renewal_Countc__c', 'renewalCount', changedFields)) changed = true;

        return changed ? updateAcc : null;
    }
    
    private Boolean checkAndSetField(Account updateAcc, Account oldAcc, Map<String, Object> metrics, String fieldName, String metricKey, List<String> changedFields) {
        Object newValue = metrics.get(metricKey);
        Object oldValue = oldAcc.get(fieldName);

        // Handle decimal/number comparisons more carefully
        if (hasValueChanged(newValue, oldValue)) {
            updateAcc.put(fieldName, newValue);
            changedFields.add(fieldName);
            return true;
        }
        return false;
    }

    private Boolean hasValueChanged(Object newValue, Object oldValue) {
        // Handle null comparisons
        if (newValue == null && oldValue == null) return false;
        if (newValue == null || oldValue == null) return true;

        // Handle decimal comparisons with precision
        if (newValue instanceof Decimal && oldValue instanceof Decimal) {
            Decimal newDecimal = (Decimal) newValue;
            Decimal oldDecimal = (Decimal) oldValue;
            return newDecimal.setScale(2) != oldDecimal.setScale(2);
        }

        // Handle integer comparisons
        if (newValue instanceof Integer && oldValue instanceof Integer) {
            return (Integer) newValue != (Integer) oldValue;
        }

        // Handle date comparisons
        if (newValue instanceof Date && oldValue instanceof Date) {
            return (Date) newValue != (Date) oldValue;
        }

        // Default comparison
        return newValue != oldValue;
    }
    
    private void logAccountUpdate(Id accountId, Account oldAcc, Map<String, Object> metrics) {
        String details = 'Account: ' + accountId + ' | Updated fields: ';
        List<String> changes = new List<String>();

        // Log all field changes with concise format
        if (metrics.get('subscriptionCount') != oldAcc.Active_Subscription_Countc__c) {
            changes.add('Sub_Count: ' + oldAcc.Active_Subscription_Countc__c + '->' + metrics.get('subscriptionCount'));
        }
        if (metrics.get('totalRevenue') != oldAcc.Total_Revenuec__c) {
            changes.add('Revenue: ' + oldAcc.Total_Revenuec__c + '->' + metrics.get('totalRevenue'));
        }
        if (metrics.get('totalRenewalAmount') != oldAcc.Total_Renewal_Amountc__c) {
            changes.add('Total_Renewal_Amt: ' + oldAcc.Total_Renewal_Amountc__c + '->' + metrics.get('totalRenewalAmount'));
        }
        if (metrics.get('totalRenewalAmount') != oldAcc.Renewal_Amountc__c) {
            changes.add('Renewal_Amt: ' + oldAcc.Renewal_Amountc__c + '->' + metrics.get('totalRenewalAmount'));
        }
        if (metrics.get('totalAmountPaid') != oldAcc.Total_Amount_Paidc__c) {
            changes.add('Paid: ' + oldAcc.Total_Amount_Paidc__c + '->' + metrics.get('totalAmountPaid'));
        }
        if (metrics.get('totalAmountRemaining') != oldAcc.Total_Amount_Remainingc__c) {
            changes.add('Remaining: ' + oldAcc.Total_Amount_Remainingc__c + '->' + metrics.get('totalAmountRemaining'));
        }
        if (metrics.get('averageSubscriptionValue') != oldAcc.Average_Subscription_Valuec__c) {
            changes.add('Avg_Sub: ' + oldAcc.Average_Subscription_Valuec__c + '->' + metrics.get('averageSubscriptionValue'));
        }
        if (metrics.get('opportunityCount') != oldAcc.Opportunity_Countc__c) {
            changes.add('Opp_Count: ' + oldAcc.Opportunity_Countc__c + '->' + metrics.get('opportunityCount'));
        }
        if (metrics.get('openOpportunities') != oldAcc.Open_Opportunitiesc__c) {
            changes.add('Open_Opps: ' + oldAcc.Open_Opportunitiesc__c + '->' + metrics.get('openOpportunities'));
        }
        if (metrics.get('wonValue') != oldAcc.Won_Valuec__c) {
            changes.add('Won: ' + oldAcc.Won_Valuec__c + '->' + metrics.get('wonValue'));
        }
        if (metrics.get('lostValue') != oldAcc.Lost_Valuec__c) {
            changes.add('Lost: ' + oldAcc.Lost_Valuec__c + '->' + metrics.get('lostValue'));
        }
        if (metrics.get('annualContractValue') != oldAcc.Annual_Contract_Valuec__c) {
            changes.add('ACV: ' + oldAcc.Annual_Contract_Valuec__c + '->' + metrics.get('annualContractValue'));
        }
        if (metrics.get('mrr') != oldAcc.MRRc__c) {
            changes.add('MRR: ' + oldAcc.MRRc__c + '->' + metrics.get('mrr'));
        }
        if (metrics.get('arr') != oldAcc.ARRc__c) {
            changes.add('ARR: ' + oldAcc.ARRc__c + '->' + metrics.get('arr'));
        }
        if (metrics.get('closedWonACV') != oldAcc.Closed_Won_ACVc__c) {
            changes.add('Closed_Won_ACV: ' + oldAcc.Closed_Won_ACVc__c + '->' + metrics.get('closedWonACV'));
        }
        if (metrics.get('closedWonCount') != oldAcc.Closed_Won_Countc__c) {
            changes.add('Closed_Won_Count: ' + oldAcc.Closed_Won_Countc__c + '->' + metrics.get('closedWonCount'));
        }
        if (metrics.get('openRenewals') != oldAcc.Open_Renewalsc__c) {
            changes.add('Open_Renewals: ' + oldAcc.Open_Renewalsc__c + '->' + metrics.get('openRenewals'));
        }
        if (metrics.get('proformaInvoiceValue') != oldAcc.Proforma_Invoice_Valuec__c) {
            changes.add('Proforma_Value: ' + oldAcc.Proforma_Invoice_Valuec__c + '->' + metrics.get('proformaInvoiceValue'));
        }
        if (metrics.get('proformaInvoiceCount') != oldAcc.Proforma_Invoice_Countc__c) {
            changes.add('Proforma_Count: ' + oldAcc.Proforma_Invoice_Countc__c + '->' + metrics.get('proformaInvoiceCount'));
        }
        if (metrics.get('renewalCount') != oldAcc.Renewal_Countc__c) {
            changes.add('Renewal_Count: ' + oldAcc.Renewal_Countc__c + '->' + metrics.get('renewalCount'));
        }

        if (!changes.isEmpty()) {
            // Format with line breaks for better readability
            String formattedChanges = String.join(changes, ',\n');
            details += formattedChanges;
            logEvent('Account Metrics Updated', details, true);
        }
    }
    
    private void logEvent(String eventName, String details, Boolean isSuccess) {
        try {
            KGRenewal__KineticGrowthLog__c log = new KGRenewal__KineticGrowthLog__c(
                Name = eventName,
                KGRenewal__OperationId__c = operationId,
                KGRenewal__Details__c = details,
                KGRenewal__IsSuccess__c = isSuccess,
                KGRenewal__Timestamp__c = String.valueOf(System.now())
            );
            pendingLogs.add(log);

            // Batch insert logs when we have 50 or more to avoid DML limits
            if (pendingLogs.size() >= 50) {
                flushLogs();
            }
        } catch (Exception e) {
            System.debug('Failed to create log: ' + e.getMessage());
        }
    }

    private void flushLogs() {
        if (!pendingLogs.isEmpty()) {
            try {
                insert pendingLogs;
                pendingLogs.clear();
            } catch (Exception e) {
                System.debug('Failed to insert logs: ' + e.getMessage());
                pendingLogs.clear(); // Clear to prevent accumulation
            }
        }
    }

    // Helper method to check if metrics contain any non-zero values
    private Boolean hasNonZeroMetrics(Map<String, Object> metrics) {
        if (metrics == null || metrics.isEmpty()) {
            return false;
        }

        // Check key metrics to determine if account has meaningful data
        Integer subscriptionCount = (Integer) metrics.get('subscriptionCount');
        Decimal totalRevenue = (Decimal) metrics.get('totalRevenue');
        Integer opportunityCount = (Integer) metrics.get('opportunityCount');
        Decimal wonValue = (Decimal) metrics.get('wonValue');
        Integer renewalCount = (Integer) metrics.get('renewalCount');

        return (subscriptionCount != null && subscriptionCount > 0) ||
               (totalRevenue != null && totalRevenue > 0) ||
               (opportunityCount != null && opportunityCount > 0) ||
               (wonValue != null && wonValue > 0) ||
               (renewalCount != null && renewalCount > 0);
    }
}
