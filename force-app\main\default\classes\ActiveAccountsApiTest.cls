@isTest
private class ActiveAccountsApiTest {
    
    @testSetup
    static void setup() {
        // Create minimal test data without read-only fields
        List<Account> testAccounts = new List<Account>();
        for (Integer i = 0; i < 3; i++) {
            testAccounts.add(new Account(
                Name = 'Test Account ' + i,
                Tenant_ID__c = 8000 + i
            ));
        }
        insert testAccounts;
        
        // Create basic opportunities
        List<Opportunity> testOpps = new List<Opportunity>();
        for (Account acc : testAccounts) {
            testOpps.add(new Opportunity(
                Name = 'Test Opp ' + acc.Name,
                AccountId = acc.Id,
                StageName = 'Closed Won',
                CloseDate = Date.today(),
                Amount = 10000
            ));
        }
        insert testOpps;
    }
    
    @isTest
    static void testGetActiveAccountsBasic() {
        RestRequest req = new RestRequest();
        RestResponse res = new RestResponse();
        req.requestURI = '/services/apexrest/active-accounts';
        req.httpMethod = 'GET';
        
        RestContext.request = req;
        RestContext.response = res;
        
        Test.startTest();
        ActiveAccountsApi.getActiveAccounts();
        Test.stopTest();
        
        System.assertEquals(200, res.statusCode, 'Should return 200 status code');
        System.assertEquals('application/json', res.headers.get('Content-Type'), 'Should return JSON content type');
        System.assertNotEquals(null, res.responseBody, 'Should have response body');
    }
    
    @isTest
    static void testGetActiveAccountsWithYear() {
        RestRequest req = new RestRequest();
        RestResponse res = new RestResponse();
        req.requestURI = '/services/apexrest/active-accounts?year=2024';
        req.httpMethod = 'GET';
        
        RestContext.request = req;
        RestContext.response = res;
        
        Test.startTest();
        ActiveAccountsApi.getActiveAccounts();
        Test.stopTest();
        
        System.assertEquals(200, res.statusCode, 'Should return 200 status code');
        System.assertEquals('application/json', res.headers.get('Content-Type'), 'Should return JSON content type');
        System.assertNotEquals(null, res.responseBody, 'Should have response body');
    }
    
    @isTest
    static void testGetActiveAccountsEmpty() {
        // Delete test data to test empty scenario
        delete [SELECT Id FROM Opportunity];
        delete [SELECT Id FROM Account];
        
        RestRequest req = new RestRequest();
        RestResponse res = new RestResponse();
        req.requestURI = '/services/apexrest/active-accounts';
        req.httpMethod = 'GET';
        
        RestContext.request = req;
        RestContext.response = res;
        
        Test.startTest();
        ActiveAccountsApi.getActiveAccounts();
        Test.stopTest();
        
        System.assertEquals(200, res.statusCode, 'Should return 200 status code');
        System.assertEquals('application/json', res.headers.get('Content-Type'), 'Should return JSON content type');
        System.assertNotEquals(null, res.responseBody, 'Should have response body');
    }

    @isTest
    static void testAccountWithNoOpportunitiesOrSubscriptions() {
        Account acc = new Account(Name = 'No Data Account', Tenant_ID__c = 9991);
        insert acc;
        RestRequest req = new RestRequest();
        RestResponse res = new RestResponse();
        req.requestURI = '/services/apexrest/active-accounts';
        req.httpMethod = 'GET';
        RestContext.request = req;
        RestContext.response = res;
        Test.startTest();
        ActiveAccountsApi.getActiveAccounts();
        Test.stopTest();
        System.assertEquals(200, res.statusCode);
        System.assertNotEquals(null, res.responseBody);
    }
    @isTest
    static void testAccountWithOnlyLostOpportunities() {
        Account acc = new Account(Name = 'Lost Only Account', Tenant_ID__c = 9992);
        insert acc;
        Opportunity lostOpp = new Opportunity(Name = 'Lost Opp', AccountId = acc.Id, StageName = 'Closed Lost', CloseDate = Date.today(), Amount = 1000);
        insert lostOpp;
        RestRequest req = new RestRequest();
        RestResponse res = new RestResponse();
        req.requestURI = '/services/apexrest/active-accounts';
        req.httpMethod = 'GET';
        RestContext.request = req;
        RestContext.response = res;
        Test.startTest();
        ActiveAccountsApi.getActiveAccounts();
        Test.stopTest();
        System.assertEquals(200, res.statusCode);
        System.assertNotEquals(null, res.responseBody);
    }
    @isTest
    static void testAccountWithOnlyInactiveSubscriptions() {
        Account acc = new Account(Name = 'Inactive Sub Account', Tenant_ID__c = 9993);
        insert acc;
        // Simulate inactive by setting end date in the past and provide required start date
        KGRenewal__Subscription__c sub = new KGRenewal__Subscription__c(
            KGRenewal__Account__c = acc.Id,
            KGRenewal__StartDate__c = Date.today().addDays(-60),
            KGRenewal__EndDate__c = Date.today().addDays(-30)
        );
        insert sub;
        RestRequest req = new RestRequest();
        RestResponse res = new RestResponse();
        req.requestURI = '/services/apexrest/active-accounts';
        req.httpMethod = 'GET';
        RestContext.request = req;
        RestContext.response = res;
        Test.startTest();
        ActiveAccountsApi.getActiveAccounts();
        Test.stopTest();
        System.assertEquals(200, res.statusCode);
        System.assertNotEquals(null, res.responseBody);
    }
    @isTest
    static void testAccountWithBothButNotActive() {
        Account acc = new Account(Name = 'Inactive Combo Account', Tenant_ID__c = 9994);
        insert acc;
        Opportunity lostOpp = new Opportunity(Name = 'Lost Opp', AccountId = acc.Id, StageName = 'Closed Lost', CloseDate = Date.today(), Amount = 1000);
        insert lostOpp;
        // Simulate inactive by setting end date in the past and provide required start date
        KGRenewal__Subscription__c sub = new KGRenewal__Subscription__c(
            KGRenewal__Account__c = acc.Id,
            KGRenewal__StartDate__c = Date.today().addDays(-60),
            KGRenewal__EndDate__c = Date.today().addDays(-30)
        );
        insert sub;
        RestRequest req = new RestRequest();
        RestResponse res = new RestResponse();
        req.requestURI = '/services/apexrest/active-accounts';
        req.httpMethod = 'GET';
        RestContext.request = req;
        RestContext.response = res;
        Test.startTest();
        ActiveAccountsApi.getActiveAccounts();
        Test.stopTest();
        System.assertEquals(200, res.statusCode);
        System.assertNotEquals(null, res.responseBody);
    }
    @isTest
    static void testFieldFilteringWithMissingAndExtraFields() {
        RestRequest req = new RestRequest();
        RestResponse res = new RestResponse();
        req.requestURI = '/services/apexrest/active-accounts?fields=accountName,nonexistentField,totalRevenue';
        req.httpMethod = 'GET';
        RestContext.request = req;
        RestContext.response = res;
        Test.startTest();
        ActiveAccountsApi.getActiveAccounts();
        Test.stopTest();
        System.assertEquals(200, res.statusCode);
        System.assertNotEquals(null, res.responseBody);
    }
    @isTest
    static void testErrorHandlingEmptyData() {
        // Delete all data
        delete [SELECT Id FROM KGRenewal__Subscription__c];
        delete [SELECT Id FROM Opportunity];
        delete [SELECT Id FROM Account];
        RestRequest req = new RestRequest();
        RestResponse res = new RestResponse();
        req.requestURI = '/services/apexrest/active-accounts';
        req.httpMethod = 'GET';
        RestContext.request = req;
        RestContext.response = res;
        Test.startTest();
        ActiveAccountsApi.getActiveAccounts();
        Test.stopTest();
        System.assertEquals(200, res.statusCode);
        System.assertNotEquals(null, res.responseBody);
    }

    @isTest
    static void testGetActiveAccountsWithDateRange() {
        RestRequest req = new RestRequest();
        RestResponse res = new RestResponse();
        req.requestURI = '/services/apexrest/active-accounts?startDate=2024-01-01&endDate=2024-12-31';
        req.httpMethod = 'GET';

        RestContext.request = req;
        RestContext.response = res;

        Test.startTest();
        ActiveAccountsApi.getActiveAccounts();
        Test.stopTest();

        System.assertEquals(200, res.statusCode, 'Should return 200 status');
        System.assertEquals('application/json', res.headers.get('Content-Type'), 'Should return JSON content type');
        System.assertNotEquals(null, res.responseBody, 'Should have response body');
    }

    @isTest
    static void testGetActiveAccountsWithThisYear() {
        RestRequest req = new RestRequest();
        RestResponse res = new RestResponse();
        req.requestURI = '/services/apexrest/active-accounts?thisYear=true';
        req.httpMethod = 'GET';

        RestContext.request = req;
        RestContext.response = res;

        Test.startTest();
        ActiveAccountsApi.getActiveAccounts();
        Test.stopTest();

        System.assertEquals(200, res.statusCode, 'Should return 200 status');
        System.assertEquals('application/json', res.headers.get('Content-Type'), 'Should return JSON content type');
        System.assertNotEquals(null, res.responseBody, 'Should have response body');
    }

    @isTest
    static void testGetActiveAccountsWithInvalidDateFormat() {
        RestRequest req = new RestRequest();
        RestResponse res = new RestResponse();
        req.requestURI = '/services/apexrest/active-accounts?startDate=invalid-date&endDate=2024-12-31';
        req.httpMethod = 'GET';

        RestContext.request = req;
        RestContext.response = res;

        Test.startTest();
        ActiveAccountsApi.getActiveAccounts();
        Test.stopTest();

        // API handles invalid dates gracefully and returns 200 with empty results
        System.assertEquals(200, res.statusCode, 'Should return 200 status even with invalid date');
        System.assertEquals('application/json', res.headers.get('Content-Type'), 'Should return JSON content type');
        System.assertNotEquals(null, res.responseBody, 'Should have response body');
    }
}