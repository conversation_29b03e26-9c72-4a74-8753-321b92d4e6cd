@RestResource(urlMapping='/active-accounts')
global with sharing class ActiveAccountsApi {
    @HttpGet
    global static void getActiveAccounts() {
        RestRequest req = RestContext.request;
        RestResponse res = RestContext.response;
        res.addHeader('Content-Type', 'application/json');
        
        try {
            // Parse date filter parameters
            Date filterStartDate = null;
            Date filterEndDate = null;
            
            // Handle different date filter options
            if (req.params != null && req.params.containsKey('startDate') && req.params.containsKey('endDate')) {
                // Specific date range
                filterStartDate = Date.valueOf(req.params.get('startDate'));
                filterEndDate = Date.valueOf(req.params.get('endDate'));
            } else if (req.params != null && req.params.containsKey('year')) {
                // Specific year
                Integer year = Integer.valueOf(req.params.get('year'));
                filterStartDate = Date.newInstance(year, 1, 1);
                filterEndDate = Date.newInstance(year, 12, 31);
            } else if (req.params != null && req.params.containsKey('thisYear') && req.params.get('thisYear').toLowerCase() == 'true') {
                // This year
                Integer currentYear = Date.today().year();
                filterStartDate = Date.newInstance(currentYear, 1, 1);
                filterEndDate = Date.newInstance(currentYear, 12, 31);
            } else if (req.params != null && req.params.containsKey('quarter') && req.params.containsKey('year')) {
                // Specific quarter
                Integer year = Integer.valueOf(req.params.get('year'));
                Integer quarter = Integer.valueOf(req.params.get('quarter'));
                if (quarter >= 1 && quarter <= 4) {
                    Integer startMonth = (quarter - 1) * 3 + 1;
                    Integer endMonth = quarter * 3;
                    filterStartDate = Date.newInstance(year, startMonth, 1);
                    filterEndDate = Date.newInstance(year, endMonth, Date.daysInMonth(year, endMonth));
                }
            }
            
            // Query all Accounts with active Opportunities
            List<Account> accounts = [SELECT Id, Name, Tenant_ID__c FROM Account];
            Map<Id, Account> accountMap = new Map<Id, Account>(accounts);
            Set<Id> accountIds = accountMap.keySet();
            
            // Query all Opportunities for these Accounts
            String oppQuery = 'SELECT Id, AccountId, Amount, StageName, Name, CloseDate, ' +
                             'KGRenewal__ContractEffectiveDate__c, KGRenewal__ContractEndDate__c, KGRenewal__Term__c ' +
                             'FROM Opportunity WHERE AccountId IN :accountIds';
            
            // Add date filtering to opportunity query if date filters are specified
            if (filterStartDate != null && filterEndDate != null) {
                oppQuery += ' AND KGRenewal__ContractEffectiveDate__c >= :filterStartDate AND KGRenewal__ContractEffectiveDate__c <= :filterEndDate';
            }
            
            List<Opportunity> allOpps = Database.query(oppQuery);
            
            // Query all Subscriptions for these Accounts
            List<KGRenewal__Subscription__c> allSubs = [
                SELECT Id, KGRenewal__Account__c, KGRenewal__StartDate__c, KGRenewal__EndDate__c, 
                       KGRenewal__BillingPeriod__c, KGRenewal__BillingFrequency__c, KGRenewal__IsActiveTerm__c,
                       KGRenewal__TotalAmount__c, KGRenewal__RenewalAmount__c, KGRenewal__Amount__c, 
                       KGRenewal__AmountPaid__c, KGRenewal__AmountRemaining__c, KGRenewal__RenewalDate__c
                FROM KGRenewal__Subscription__c
                WHERE KGRenewal__Account__c IN :accountIds
            ];
            
            // Query all Subscription Products for these Subscriptions
            Set<Id> subIds = new Set<Id>();
            for (KGRenewal__Subscription__c sub : allSubs) {
                subIds.add(sub.Id);
            }
            List<KGRenewal__SubscriptionProduct__c> allSubProducts = new List<KGRenewal__SubscriptionProduct__c>();
            Map<Id, List<KGRenewal__SubscriptionProduct__c>> subProductsBySub = new Map<Id, List<KGRenewal__SubscriptionProduct__c>>();
            if (!subIds.isEmpty()) {
                allSubProducts = [
                    SELECT Id, KGRenewal__Subscription__c, KGRenewal__TotalPrice__c, KGRenewal__Term__c, KGRenewal__IsCurrent__c
                    FROM KGRenewal__SubscriptionProduct__c
                    WHERE KGRenewal__Subscription__c IN :subIds AND KGRenewal__IsCurrent__c = true
                ];
                for (KGRenewal__SubscriptionProduct__c sp : allSubProducts) {
                    if (!subProductsBySub.containsKey(sp.KGRenewal__Subscription__c)) {
                        subProductsBySub.put(sp.KGRenewal__Subscription__c, new List<KGRenewal__SubscriptionProduct__c>());
                    }
                    subProductsBySub.get(sp.KGRenewal__Subscription__c).add(sp);
                }
            }
            
            // Group data by Account
            Map<Id, List<Opportunity>> oppsByAccount = new Map<Id, List<Opportunity>>();
            Map<Id, List<KGRenewal__Subscription__c>> subsByAccount = new Map<Id, List<KGRenewal__Subscription__c>>();
            
            for (Opportunity opp : allOpps) {
                if (!oppsByAccount.containsKey(opp.AccountId)) {
                    oppsByAccount.put(opp.AccountId, new List<Opportunity>());
                }
                oppsByAccount.get(opp.AccountId).add(opp);
            }
            
            for (KGRenewal__Subscription__c sub : allSubs) {
                if (!subsByAccount.containsKey(sub.KGRenewal__Account__c)) {
                    subsByAccount.put(sub.KGRenewal__Account__c, new List<KGRenewal__Subscription__c>());
                }
                subsByAccount.get(sub.KGRenewal__Account__c).add(sub);
            }
            
            List<Map<String, Object>> activeAccounts = new List<Map<String, Object>>();
            Date today = Date.today();
            
            for (Account acc : accounts) {
                List<Opportunity> opps = oppsByAccount.containsKey(acc.Id) ? oppsByAccount.get(acc.Id) : new List<Opportunity>();
                List<KGRenewal__Subscription__c> subs = subsByAccount.containsKey(acc.Id) ? subsByAccount.get(acc.Id) : new List<KGRenewal__Subscription__c>();
                
                // Check if account has active opportunities (either with active subscriptions or without subscriptions)
                Boolean hasActiveOpp = false;
                Decimal totalACV = 0;
                Date contractStartDate;
                Date contractEndDate;
                String billingPeriod;
                String billingFrequency;
                
                // Subscription metrics
                Integer subscriptionCount = 0;
                Decimal totalRevenue = 0;
                Decimal totalRenewalAmount = 0;
                Decimal totalAmountPaid = 0;
                Decimal totalAmountRemaining = 0;
                Date lastRenewalDate;
                Decimal accountMRR = 0;
                Decimal accountARR = 0;
                
                // Opportunity metrics
                Integer opportunityCount = 0;
                Decimal wonValue = 0;
                Decimal lostValue = 0;
                Decimal negotiationValue = 0;
                Integer lostCount = 0;
                Integer openOpportunities = 0;
                
                for (Opportunity opp : opps) {
                    opportunityCount++;
                    
                    // Check if opportunity is active (current date is between contract dates)
                    Boolean isActive = false;
                    if (opp.KGRenewal__ContractEffectiveDate__c != null && opp.KGRenewal__ContractEndDate__c != null) {
                        if (opp.KGRenewal__ContractEffectiveDate__c <= today && opp.KGRenewal__ContractEndDate__c >= today) {
                            isActive = true;
                        }
                    }
                    
                    // If date filters are specified, also check if opportunity falls within the filter range
                    Boolean withinDateRange = true;
                    if (filterStartDate != null && filterEndDate != null) {
                        if (opp.KGRenewal__ContractEffectiveDate__c == null || 
                            opp.KGRenewal__ContractEffectiveDate__c < filterStartDate || 
                            opp.KGRenewal__ContractEffectiveDate__c > filterEndDate) {
                            withinDateRange = false;
                        }
                    }
                    
                    if (isActive && withinDateRange) {
                        hasActiveOpp = true;
                        
                        // Calculate ACV
                        Decimal acv = 0;
                        if (opp.KGRenewal__Term__c != null && opp.KGRenewal__Term__c > 0) {
                            if (opp.KGRenewal__Term__c == 12) {
                                acv = (opp.Amount != null ? opp.Amount : 0);
                            } else {
                                Decimal termYears = opp.KGRenewal__Term__c / 12;
                                if (opp.Amount != null && termYears > 0) {
                                    acv = opp.Amount / termYears;
                                }
                            }
                        } else if (opp.KGRenewal__ContractEffectiveDate__c != null && opp.KGRenewal__ContractEndDate__c != null && opp.KGRenewal__ContractEndDate__c > opp.KGRenewal__ContractEffectiveDate__c) {
                            Decimal termYears = ((Decimal) opp.KGRenewal__ContractEndDate__c.daysBetween(opp.KGRenewal__ContractEffectiveDate__c)) / 365;
                            if (opp.Amount != null && termYears > 0) {
                                acv = opp.Amount / termYears;
                            }
                        }
                        totalACV += acv;
                        
                        // Get contract dates (use earliest start and latest end)
                        if (contractStartDate == null || (opp.KGRenewal__ContractEffectiveDate__c != null && opp.KGRenewal__ContractEffectiveDate__c < contractStartDate)) {
                            contractStartDate = opp.KGRenewal__ContractEffectiveDate__c;
                        }
                        if (contractEndDate == null || (opp.KGRenewal__ContractEndDate__c != null && opp.KGRenewal__ContractEndDate__c > contractEndDate)) {
                            contractEndDate = opp.KGRenewal__ContractEndDate__c;
                        }
                    }
                    
                    // Opportunity stage analysis
                    if (opp.StageName == 'Closed Won') {
                        wonValue += (opp.Amount != null ? opp.Amount : 0);
                    } else if (opp.StageName == 'Closed Lost') {
                        lostValue += (opp.Amount != null ? opp.Amount : 0);
                        lostCount++;
                    } else {
                        negotiationValue += (opp.Amount != null ? opp.Amount : 0);
                        openOpportunities++;
                    }
                }
                
                // Process subscription data
                for (KGRenewal__Subscription__c sub : subs) {
                    if (sub.KGRenewal__IsActiveTerm__c == true) {
                        subscriptionCount++;
                        
                        // Revenue metrics
                        totalRevenue += (sub.KGRenewal__TotalAmount__c != null ? sub.KGRenewal__TotalAmount__c : 0);
                        totalRenewalAmount += (sub.KGRenewal__RenewalAmount__c != null ? sub.KGRenewal__RenewalAmount__c : 0);
                        totalAmountPaid += (sub.KGRenewal__AmountPaid__c != null ? sub.KGRenewal__AmountPaid__c : 0);
                        totalAmountRemaining += (sub.KGRenewal__AmountRemaining__c != null ? sub.KGRenewal__AmountRemaining__c : 0);
                        
                        // Renewal date
                        if (lastRenewalDate == null || (sub.KGRenewal__RenewalDate__c != null && sub.KGRenewal__RenewalDate__c > lastRenewalDate)) {
                            lastRenewalDate = sub.KGRenewal__RenewalDate__c;
                        }
                        
                        // Billing information
                        if (billingPeriod == null && sub.KGRenewal__BillingPeriod__c != null) {
                            billingPeriod = String.valueOf(sub.KGRenewal__BillingPeriod__c);
                        }
                        if (billingFrequency == null && sub.KGRenewal__BillingFrequency__c != null) {
                            billingFrequency = String.valueOf(sub.KGRenewal__BillingFrequency__c);
                        }
                        
                        // ARR and MRR from Subscription Products
                        List<KGRenewal__SubscriptionProduct__c> subProducts = subProductsBySub.containsKey(sub.Id) ? subProductsBySub.get(sub.Id) : new List<KGRenewal__SubscriptionProduct__c>();
                        for (KGRenewal__SubscriptionProduct__c sp : subProducts) {
                            if (sp.KGRenewal__TotalPrice__c != null && sp.KGRenewal__Term__c != null && sp.KGRenewal__Term__c > 0) {
                                accountMRR += sp.KGRenewal__TotalPrice__c / sp.KGRenewal__Term__c;
                            }
                        }
                    }
                }
                
                accountARR = accountMRR * 12;
                
                // Only include accounts with active opportunities AND active subscriptions
                if (hasActiveOpp) {
                    // Check if account has active subscriptions
                    Boolean hasActiveSub = false;
                    for (KGRenewal__Subscription__c sub : subs) {
                        if (sub.KGRenewal__IsActiveTerm__c == true) {
                            hasActiveSub = true;
                            break;
                        }
                    }
                    
                    if (hasActiveSub) {
                        Map<String, Object> accountData = new Map<String, Object>{
                            'accountName' => acc.Name,
                            'tenantId' => acc.Tenant_ID__c,
                            'contractStartDate' => contractStartDate != null ? contractStartDate.format() : null,
                            'contractEndDate' => contractEndDate != null ? contractEndDate.format() : null,
                            'billingPeriod' => billingPeriod,
                            'billingFrequency' => billingFrequency,
                            'acv' => totalACV.setScale(2),
                            'subscriptionCount' => subscriptionCount,
                            'totalRevenue' => totalRevenue.setScale(2),
                            'totalRenewalAmount' => totalRenewalAmount.setScale(2),
                            'totalAmountPaid' => totalAmountPaid.setScale(2),
                            'totalAmountRemaining' => totalAmountRemaining.setScale(2),
                            'renewalDate' => lastRenewalDate != null ? lastRenewalDate.format() : null,
                            'accountMRR' => accountMRR.setScale(2),
                            'accountARR' => accountARR.setScale(2),
                            'opportunityCount' => opportunityCount,
                            'wonValue' => wonValue.setScale(2),
                            'lostValue' => lostValue.setScale(2),
                            'negotiationValue' => negotiationValue.setScale(2),
                            'lostCount' => lostCount,
                            'openOpportunities' => openOpportunities
                        };
                        activeAccounts.add(accountData);
                    }
                }
            }
            
            Map<String, Object> response = new Map<String, Object>{
                'records' => activeAccounts,
                'totalSize' => activeAccounts.size(),
                'done' => true
            };
            
            // Add date filter information to response if filters were applied
            if (filterStartDate != null && filterEndDate != null) {
                response.put('dateFilter', new Map<String, Object>{
                    'startDate' => filterStartDate.format(),
                    'endDate' => filterEndDate.format(),
                    'applied' => true
                });
            } else {
                response.put('dateFilter', new Map<String, Object>{
                    'applied' => false
                });
            }
            
            res.responseBody = Blob.valueOf(JSON.serialize(response));
            res.statusCode = 200;
            
        } catch (Exception e) {
            res.statusCode = 500;
            res.responseBody = Blob.valueOf(JSON.serialize(new Map<String, String>{
                'error' => e.getMessage(), 
                'stackTrace' => e.getStackTraceString()
            }));
        }
    }
}