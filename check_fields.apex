// Check if Renewal_Amountc__c field exists (without "Total_")

List<Account> accounts = [SELECT Id, Name FROM Account LIMIT 1];
if (!accounts.isEmpty()) {
    Account acc = accounts[0];
    
    // Try to query both field variations to see which exist
    try {
        List<Account> testQuery1 = [SELECT Id, Total_Renewal_Amountc__c FROM Account WHERE Id = :acc.Id LIMIT 1];
        System.debug('✅ Total_Renewal_Amountc__c field EXISTS');
    } catch (Exception e) {
        System.debug('❌ Total_Renewal_Amountc__c field does NOT exist: ' + e.getMessage());
    }
    
    try {
        List<Account> testQuery2 = [SELECT Id, Renewal_Amountc__c FROM Account WHERE Id = :acc.Id LIMIT 1];
        System.debug('✅ Renewal_Amountc__c field EXISTS');
    } catch (Exception e) {
        System.debug('❌ Renewal_Amountc__c field does NOT exist: ' + e.getMessage());
    }
    
    try {
        List<Account> testQuery3 = [SELECT Id, Renewal_Countc__c FROM Account WHERE Id = :acc.Id LIMIT 1];
        System.debug('✅ Renewal_Countc__c field EXISTS');
    } catch (Exception e) {
        System.debug('❌ Renewal_Countc__c field does NOT exist: ' + e.getMessage());
    }
}

// Check what fields are actually being queried in the batch
System.debug('\n=== BATCH QUERY FIELDS ===');
System.debug('The batch queries these fields:');
System.debug('- Total_Renewal_Amountc__c (mapped to totalRenewalAmount)');
System.debug('- Renewal_Countc__c (mapped to renewalCount)');

// Check if there are any validation rules or field-level security issues
System.debug('\n=== CHECKING RECENT BATCH LOGS ===');
List<KGRenewal__KineticGrowthLog__c> recentLogs = [SELECT Id, Name, KGRenewal__Details__c, KGRenewal__IsSuccess__c, CreatedDate
                                                  FROM KGRenewal__KineticGrowthLog__c 
                                                  WHERE Name LIKE '%Batch%' OR Name LIKE '%Account%'
                                                  ORDER BY CreatedDate DESC 
                                                  LIMIT 10];

for (KGRenewal__KineticGrowthLog__c log : recentLogs) {
    System.debug('Log: ' + log.Name + ' - Success: ' + log.KGRenewal__IsSuccess__c + ' - ' + log.KGRenewal__Details__c);
}
