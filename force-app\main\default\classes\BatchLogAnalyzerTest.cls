@isTest
private class BatchLogAnalyzerTest {
    
    @testSetup
    static void setup() {
        // Create test batch log records
        List<KGRenewal__KineticGrowthLog__c> testLogs = new List<KGRenewal__KineticGrowthLog__c>();
        // Use valid 18-char operation IDs
        String batchId1 = 'BATCH-0000********';
        String batchId2 = 'BATCH-************';
        
        testLogs.add(new KGRenewal__KineticGrowthLog__c(
            KGRenewal__OperationId__c = batchId1,
            Name = 'Account Sales Metrics Batch Started',
            KGRenewal__Details__c = 'Batch Operation ID: ' + batchId1 + '\nJob ID: 707TI00000lGzkg',
            KGRenewal__IsSuccess__c = true,
            KGRenewal__Timestamp__c = '2024-12-01 14:30:22'
        ));
        
        testLogs.add(new KGRenewal__KineticGrowthLog__c(
            KGRenewal__OperationId__c = batchId1,
            Name = 'Account Metrics Updated',
            <PERSON><PERSON><PERSON><PERSON>__Details__c = 'Account: Test Account 1 (001TI000004abcd)\nChanged Fields: ARRc__c, MRRc__c\n\nField Changes:\nARRc__c: 120000.00 → 145000.00\nMRRc__c: 10000.00 → 12083.33',
            KGRenewal__IsSuccess__c = true,
            KGRenewal__Timestamp__c = '2024-12-01 14:31:15'
        ));
        
        testLogs.add(new KGRenewal__KineticGrowthLog__c(
            KGRenewal__OperationId__c = batchId1,
            Name = 'Account Metrics Updated',
            KGRenewal__Details__c = 'Account: Test Account 2 (001TI000004efgh)\nChanged Fields: Annual_Contract_Valuec__c, Won_Valuec__c\n\nField Changes:\nAnnual_Contract_Valuec__c: 85000.00 → 98000.00\nWon_Valuec__c: 75000.00 → 85000.00',
            KGRenewal__IsSuccess__c = true,
            KGRenewal__Timestamp__c = '2024-12-01 14:31:20'
        ));
        
        testLogs.add(new KGRenewal__KineticGrowthLog__c(
            KGRenewal__OperationId__c = batchId1,
            Name = 'No Changes Required',
            KGRenewal__Details__c = 'Account: Test Account 3 (001TI000004ijkl) - All metrics up to date',
            KGRenewal__IsSuccess__c = true,
            KGRenewal__Timestamp__c = '2024-12-01 14:31:25'
        ));
        
        testLogs.add(new KGRenewal__KineticGrowthLog__c(
            KGRenewal__OperationId__c = batchId1,
            Name = 'Batch Completed Successfully',
            KGRenewal__Details__c = 'Batch Operation ID: ' + batchId1 + '\nJob ID: 707TI00000lGzkg\nStatus: Completed\nTotal Items: 3\nItems Processed: 3\nErrors: 0',
            KGRenewal__IsSuccess__c = true,
            KGRenewal__Timestamp__c = '2024-12-01 14:32:15'
        ));
        
        // Create logs for second batch operation with errors
        testLogs.add(new KGRenewal__KineticGrowthLog__c(
            KGRenewal__OperationId__c = batchId2,
            Name = 'Account Sales Metrics Batch Started',
            KGRenewal__Details__c = 'Batch Operation ID: ' + batchId2 + '\nJob ID: 707TI00000lGzzz',
            KGRenewal__IsSuccess__c = true,
            KGRenewal__Timestamp__c = '2024-12-01 15:00:00'
        ));
        
        testLogs.add(new KGRenewal__KineticGrowthLog__c(
            KGRenewal__OperationId__c = batchId2,
            Name = 'Batch Chunk Failed',
            KGRenewal__Details__c = 'Error processing account chunk: System.NullPointerException\nStack Trace: ...',
            KGRenewal__IsSuccess__c = false,
            KGRenewal__Timestamp__c = '2024-12-01 15:01:00'
        ));
        
        // Create non-batch logs to test filtering
        testLogs.add(new KGRenewal__KineticGrowthLog__c(
            KGRenewal__OperationId__c = 'OTHER-OP-********',
            Name = 'Other Operation',
            KGRenewal__Details__c = 'This is not a batch operation',
            KGRenewal__IsSuccess__c = true,
            KGRenewal__Timestamp__c = '2024-12-01 16:00:00'
        ));
        
        insert testLogs;
    }
    
    @isTest
    static void testGetBatchOperations() {
        Test.startTest();
        List<String> operations = BatchLogAnalyzer.getBatchOperations();
        Test.stopTest();
        
        System.assertNotEquals(0, operations.size(), 'Should return batch operations');
        
        // Verify operations are batch operations
        for(String operation : operations) {
            System.assert(operation.startsWith('BATCH-'), 'Operation should start with BATCH-');
        }
    }
    
    @isTest
    static void testGetBatchLogs() {
        String batchId = 'BATCH-0000********';
        
        Test.startTest();
        List<KGRenewal__KineticGrowthLog__c> logs = BatchLogAnalyzer.getBatchLogs(batchId);
        Test.stopTest();
        
        System.assertNotEquals(0, logs.size(), 'Should return logs for the batch');
        
        // Verify all logs belong to the requested batch
        for (KGRenewal__KineticGrowthLog__c log : logs) {
            System.assertEquals(batchId, log.KGRenewal__OperationId__c, 'All logs should belong to the requested batch');
        }
    }
    
    @isTest
    static void testGetRecentBatchSummary() {
        Test.startTest();
        Map<String, Object> summary = BatchLogAnalyzer.getRecentBatchSummary();
        Test.stopTest();
        
        // Verify summary structure
        System.assertNotEquals(null, summary, 'Summary should not be null');
        
        if(summary.containsKey('error')) {
            // If there's an error, verify it's a valid error message
            System.assertNotEquals(null, summary.get('error'), 'Error message should not be null');
        } else {
            // If no error, verify summary contains expected data
            System.assert(summary.containsKey('operationId'), 'Summary should contain operationId');
            System.assert(summary.containsKey('success'), 'Summary should contain success flag');
        }
    }
    
    @isTest
    static void testGetRecentBatchSummaryNoBatches() {
        // Delete all batch logs
        delete [SELECT Id FROM KGRenewal__KineticGrowthLog__c WHERE KGRenewal__OperationId__c LIKE 'BATCH-%'];
        
        Test.startTest();
        Map<String, Object> summary = BatchLogAnalyzer.getRecentBatchSummary();
        Test.stopTest();
        
        System.assert(summary.containsKey('error'), 'Summary should contain error when no batches exist');
        System.assertNotEquals(null, summary.get('error'), 'Error message should not be null');
    }
    
    @isTest
    static void testGetUpdatedAccounts() {
        String batchId = 'BATCH-0000********';
        
        Test.startTest();
        List<Map<String, Object>> updatedAccounts = BatchLogAnalyzer.getUpdatedAccounts(batchId);
        Test.stopTest();
        
        System.assertNotEquals(0, updatedAccounts.size(), 'Should return updated accounts');
        
        // Verify account information structure
        for (Map<String, Object> accountInfo : updatedAccounts) {
            System.assert(accountInfo.containsKey('accountName'), 'Should contain account name');
            System.assert(accountInfo.containsKey('accountId'), 'Should contain account ID');
            System.assertNotEquals(null, accountInfo.get('accountName'), 'Account name should not be null');
            System.assertNotEquals(null, accountInfo.get('accountId'), 'Account ID should not be null');
        }
    }
    
    @isTest
    static void testGetFieldChangeStats() {
        String batchId = 'BATCH-0000********';
        
        Test.startTest();
        Map<String, Integer> fieldStats = BatchLogAnalyzer.getFieldChangeStats(batchId);
        Test.stopTest();
        
        // Verify field statistics structure
        System.assertNotEquals(null, fieldStats, 'Field stats should not be null');
        
        // Verify all values are non-negative integers
        for(String fieldName : fieldStats.keySet()) {
            Integer count = fieldStats.get(fieldName);
            System.assert(count >= 0, 'Field change count should be non-negative');
        }
    }
    
    @isTest
    static void testGetBatchesWithErrors() {
        Test.startTest();
        List<Map<String, Object>> errorBatches = BatchLogAnalyzer.getBatchesWithErrors();
        Test.stopTest();
        
        System.assertNotEquals(0, errorBatches.size(), 'Should return batches with errors');
        
        // Verify error batch structure
        for(Map<String, Object> errorBatch : errorBatches) {
            System.assert(errorBatch.containsKey('operationId'), 'Should contain operation ID');
            System.assert(errorBatch.containsKey('errorCount'), 'Should contain error count');
            System.assertNotEquals(null, errorBatch.get('operationId'), 'Operation ID should not be null');
            
            Integer errorCount = (Integer)errorBatch.get('errorCount');
            System.assert(errorCount > 0, 'Error count should be positive');
        }
    }
    
    @isTest
    static void testParseAccountUpdateLog() {
        String batchId = 'BATCH-0000********';
        
        Test.startTest();
        List<Map<String, Object>> updatedAccounts = BatchLogAnalyzer.getUpdatedAccounts(batchId);
        Test.stopTest();
        
        // Verify parsing worked correctly
        if(!updatedAccounts.isEmpty()) {
            Map<String, Object> firstAccount = updatedAccounts[0];
            System.assertNotEquals(null, firstAccount.get('accountName'), 'Should parse account name');
            System.assertNotEquals(null, firstAccount.get('accountId'), 'Should parse account ID');
        }
    }
    
    @isTest
    static void testEmptyLogDetails() {
        // Create a log with empty details to test edge case
        KGRenewal__KineticGrowthLog__c emptyLog = new KGRenewal__KineticGrowthLog__c(
            KGRenewal__OperationId__c = 'BATCH-************',
            Name = 'Account Metrics Updated',
            KGRenewal__Details__c = '',
            KGRenewal__IsSuccess__c = true,
            KGRenewal__Timestamp__c = String.valueOf(Datetime.now())
        );
        insert emptyLog;
        
        Test.startTest();
        List<Map<String, Object>> updatedAccounts = BatchLogAnalyzer.getUpdatedAccounts('BATCH-************');
        Test.stopTest();
        
        // Should handle empty details gracefully - might return empty list or handle gracefully
        if(!updatedAccounts.isEmpty()) {
            Map<String, Object> accountInfo = updatedAccounts[0];
            // The method should return a map with keys, even if values are null
            // We just verify the map exists and has some structure
            System.assertNotEquals(null, accountInfo, 'Account info should not be null');
        }
        // If empty, that's also acceptable for malformed log data
    }
}