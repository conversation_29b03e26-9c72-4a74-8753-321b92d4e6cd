.highlight-badge {
    display: flex;
    align-items: center;
    margin: 1rem;
    gap: 0.5rem;
  }
  .highlight-badge .highlight-text {
    font-weight: bold;
    font-size: 0.9rem;
  }
  .highlight-text.unlimited {
    color: #9c27b0; /* Purple for unlimited clients */
  }
  .highlight-text.standard {
    color: #ff9800; /* Orange for standard clients */
  }
  .client-logo {
    width: 24px;
    height: 24px;
    object-fit: contain;
  }
  .pulsing {
    animation: pulse 1s infinite;
  }
  @keyframes pulse {
    0% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.6; transform: scale(1.1); }
    100% { opacity: 1; transform: scale(1); }
  }
  .gold {
    color: gold;
  }
  .unlimited {
    /* Purple color handled by text class */
  }
  .standard {
    filter: grayscale(100%); /* Black and white filter for standard clients */
  }
  .grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 1rem;
    padding: 1rem;
  }
  .item-card {
    display: flex;
    align-items: center;
    background: #fff;
    border-radius: 0.5rem;
    padding: 0.75rem 1rem;
    box-shadow: 0 1px 4px rgba(0,0,0,0.1);
    transition: transform 0.2s;
  }
  .item-card:hover {
    transform: scale(1.01);
  }
  .icon-wrapper {
    border-radius: 0.5rem;
    padding: 0.5rem;
    margin-right: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
  }
  .icon-wrapper.green { background: #d4edda; }
  .icon-wrapper.blue { background: #cce5ff; }
  .icon-wrapper.purple { background: #e2d6f9; }
  .icon-wrapper.orange { background: #fff3cd; }
  .icon-wrapper.red { background: #f8d7da; }
  .icon-wrapper.gray { background: #e2e3e5; }
  .label {
    font-size: 0.85rem;
    color: #666;
    margin: 0;
  }
  .value {
    font-size: 1.2rem;
    font-weight: bold;
    margin: 0;
  }