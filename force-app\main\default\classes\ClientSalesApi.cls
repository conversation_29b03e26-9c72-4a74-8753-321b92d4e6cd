@RestResource(urlMapping='/client-sales-summary')
global with sharing class ClientSalesApi {
    @HttpGet
    global static void getAllClientSalesData() {
        RestRequest req = RestContext.request;
        RestResponse res = RestContext.response;
        res.addHeader('Content-Type', 'application/json');
        
        try {
            String fieldsParam = req.params.get('fields');
            List<String> fields = new List<String>();
            if (fieldsParam != null && fieldsParam != '') {
                fields = fieldsParam.split(',');
            }

            // Query all Accounts
            List<Account> accounts = [SELECT Id, Name, Tenant_ID__c FROM Account];
            Map<Id, Account> accountMap = new Map<Id, Account>(accounts);
            Set<Id> accountIds = accountMap.keySet();

            // Query all Subscriptions for these Accounts
            List<KGRenewal__Subscription__c> allSubs = [
                SELECT Id, KGR<PERSON>wal__Account__c, K<PERSON><PERSON><PERSON>__TotalAmount__c, <PERSON><PERSON><PERSON>wal__RenewalAmount__c, <PERSON><PERSON><PERSON><PERSON>__Amount__c, <PERSON><PERSON><PERSON><PERSON>__AmountPaid__c, <PERSON><PERSON><PERSON><PERSON>__AmountRemaining__c, KGRenewal__RenewalOpportunity__c, KGRenewal__StartDate__c, KGRenewal__EndDate__c, KGRenewal__RenewalDate__c, KGRenewal__RenewalTerm__c, KGRenewal__IsAutomaticBillingEnabled__c, KGRenewal__BillingFrequency__c, KGRenewal__BillingPeriod__c
                FROM KGRenewal__Subscription__c
                WHERE KGRenewal__Account__c IN :accountIds AND KGRenewal__IsActiveTerm__c = true
            ];
            Map<Id, List<KGRenewal__Subscription__c>> subsByAccount = new Map<Id, List<KGRenewal__Subscription__c>>();
            for (KGRenewal__Subscription__c sub : allSubs) {
                if (!subsByAccount.containsKey(sub.KGRenewal__Account__c)) {
                    subsByAccount.put(sub.KGRenewal__Account__c, new List<KGRenewal__Subscription__c>());
                }
                subsByAccount.get(sub.KGRenewal__Account__c).add(sub);
            }

            // Query all Opportunities for these Accounts
            List<Opportunity> allOpps = [
                SELECT Id, AccountId, Amount, StageName, Name, CloseDate, KGRenewal__ContractEffectiveDate__c, KGRenewal__ContractEndDate__c, KGRenewal__Term__c
                FROM Opportunity
                WHERE AccountId IN :accountIds
            ];
            Map<Id, List<Opportunity>> oppsByAccount = new Map<Id, List<Opportunity>>();
            for (Opportunity opp : allOpps) {
                if (!oppsByAccount.containsKey(opp.AccountId)) {
                    oppsByAccount.put(opp.AccountId, new List<Opportunity>());
                }
                oppsByAccount.get(opp.AccountId).add(opp);
            }

            // Query all Subscription Products for these Subscriptions
            Set<Id> subIds = new Set<Id>();
            for (KGRenewal__Subscription__c sub : allSubs) {
                subIds.add(sub.Id);
            }
            List<KGRenewal__SubscriptionProduct__c> allSubProducts = new List<KGRenewal__SubscriptionProduct__c>();
            Map<Id, List<KGRenewal__SubscriptionProduct__c>> subProductsBySub = new Map<Id, List<KGRenewal__SubscriptionProduct__c>>();
            if (!subIds.isEmpty()) {
                allSubProducts = [
                    SELECT Id, KGRenewal__Subscription__c, KGRenewal__TotalPrice__c, KGRenewal__Term__c, KGRenewal__IsCurrent__c
                    FROM KGRenewal__SubscriptionProduct__c
                    WHERE KGRenewal__Subscription__c IN :subIds AND KGRenewal__IsCurrent__c = true
                ];
                for (KGRenewal__SubscriptionProduct__c sp : allSubProducts) {
                    if (!subProductsBySub.containsKey(sp.KGRenewal__Subscription__c)) {
                        subProductsBySub.put(sp.KGRenewal__Subscription__c, new List<KGRenewal__SubscriptionProduct__c>());
                    }
                    subProductsBySub.get(sp.KGRenewal__Subscription__c).add(sp);
                }
            }

            // Build summary for each account
            List<Map<String, Object>> results = new List<Map<String, Object>>();
            Date today = Date.today();
            
            // Summary totals
            Decimal totalARR = 0;
            Decimal totalMRR = 0;
            Integer totalSubscriptions = 0;
            Integer totalOpportunities = 0;
            Decimal totalACV = 0;
            Decimal totalRevenue = 0;
            
            for (Account acc : accounts) {
                Map<String, Object> result = new Map<String, Object>();
                
                // Flatten account data
                result.put('accountId', acc.Id);
                result.put('accountName', acc.Name);
                result.put('tenantId', acc.Tenant_ID__c);

                // Subscriptions
                List<KGRenewal__Subscription__c> subs = subsByAccount.containsKey(acc.Id) ? subsByAccount.get(acc.Id) : new List<KGRenewal__Subscription__c>();
                result.put('subscriptionCount', subs.size());
                totalSubscriptions += subs.size();
                
                Decimal accountRevenue = 0;
                Decimal totalRenewalAmount = 0;
                Decimal totalAmountPaid = 0;
                Decimal totalAmountRemaining = 0;
                Date lastRenewalDate;
                for (KGRenewal__Subscription__c s : subs) {
                    accountRevenue += (s.KGRenewal__TotalAmount__c != null ? s.KGRenewal__TotalAmount__c : 0);
                    totalRenewalAmount += (s.KGRenewal__RenewalAmount__c != null ? s.KGRenewal__RenewalAmount__c : 0);
                    totalAmountPaid += (s.KGRenewal__AmountPaid__c != null ? s.KGRenewal__AmountPaid__c : 0);
                    totalAmountRemaining += (s.KGRenewal__AmountRemaining__c != null ? s.KGRenewal__AmountRemaining__c : 0);
                    if (lastRenewalDate == null || (s.KGRenewal__RenewalDate__c != null && s.KGRenewal__RenewalDate__c > lastRenewalDate)) {
                        lastRenewalDate = s.KGRenewal__RenewalDate__c;
                    }
                }
                result.put('totalRevenue', accountRevenue.setScale(2));
                result.put('totalRenewalAmount', totalRenewalAmount.setScale(2));
                result.put('totalAmountPaid', totalAmountPaid.setScale(2));
                result.put('totalAmountRemaining', totalAmountRemaining.setScale(2));
                result.put('renewalDate', lastRenewalDate != null ? lastRenewalDate.format() : null);
                result.put('averageSubscriptionValue', subs.size() > 0 ? (accountRevenue / subs.size()).setScale(2) : 0);
                totalRevenue += accountRevenue;

                // Opportunities
                List<Opportunity> opps = oppsByAccount.containsKey(acc.Id) ? oppsByAccount.get(acc.Id) : new List<Opportunity>();
                result.put('opportunityCount', opps.size());
                totalOpportunities += opps.size();
                
                Decimal won = 0;
                Decimal lost = 0;
                Decimal negotiation = 0;
                Integer lostCount = 0;
                Integer openCount = 0;
                Decimal accountACV = 0;
                for (Opportunity o : opps) {
                    // ACV Calculation for current active contracts only
                    Boolean isActive = false;
                    if (o.KGRenewal__ContractEffectiveDate__c != null && o.KGRenewal__ContractEndDate__c != null) {
                        if (o.KGRenewal__ContractEffectiveDate__c <= today && o.KGRenewal__ContractEndDate__c >= today) {
                            isActive = true;
                        }
                    }
                    Decimal termYears;
                    Decimal acv = 0;
                    if (isActive) {
                        if (o.KGRenewal__Term__c != null && o.KGRenewal__Term__c > 0) {
                            if (o.KGRenewal__Term__c == 12) {
                                acv = (o.Amount != null ? o.Amount : 0);
                            } else {
                                termYears = o.KGRenewal__Term__c / 12;
                                if (o.Amount != null && termYears > 0) {
                                    acv = o.Amount / termYears;
                                }
                            }
                        } else if (o.KGRenewal__ContractEffectiveDate__c != null && o.KGRenewal__ContractEndDate__c != null && o.KGRenewal__ContractEndDate__c > o.KGRenewal__ContractEffectiveDate__c) {
                            termYears = ((Decimal) o.KGRenewal__ContractEndDate__c.daysBetween(o.KGRenewal__ContractEffectiveDate__c)) / 365;
                            if (o.Amount != null && termYears > 0) {
                                acv = o.Amount / termYears;
                            }
                        }
                        accountACV += acv;
                    }
                    if (o.StageName == 'Closed Won') {
                        won += (o.Amount != null ? o.Amount : 0);
                    } else if (o.StageName == 'Closed Lost') {
                        lost += (o.Amount != null ? o.Amount : 0);
                        lostCount++;
                    } else {
                        negotiation += (o.Amount != null ? o.Amount : 0);
                        openCount++;
                    }
                }
                result.put('annualContractValue', accountACV.setScale(2));
                result.put('wonValue', won.setScale(2));
                result.put('lostValue', lost.setScale(2));
                result.put('negotiationValue', negotiation.setScale(2));
                result.put('lostCount', lostCount);
                result.put('openOpportunities', openCount);
                totalACV += accountACV;

                // ARR and MRR from Subscription Products
                Decimal accountMRR = 0;
                Boolean hasActiveSub = !subs.isEmpty(); // Since we already filter by KGRenewal__IsActiveTerm__c = true in the query
                for (KGRenewal__Subscription__c s : subs) {
                        List<KGRenewal__SubscriptionProduct__c> subProducts = subProductsBySub.containsKey(s.Id) ? subProductsBySub.get(s.Id) : new List<KGRenewal__SubscriptionProduct__c>();
                        for (KGRenewal__SubscriptionProduct__c sp : subProducts) {
                            if (sp.KGRenewal__TotalPrice__c != null && sp.KGRenewal__Term__c != null && sp.KGRenewal__Term__c > 0) {
                                accountMRR += sp.KGRenewal__TotalPrice__c / sp.KGRenewal__Term__c;
                        }
                    }
                }
                Decimal accountARR = accountMRR * 12;
                result.put('mrr', accountMRR.setScale(2));
                result.put('arr', accountARR.setScale(2));
                totalMRR += accountMRR;
                totalARR += accountARR;

                // Field filtering
                if (!fields.isEmpty()) {
                    Map<String, Object> filtered = new Map<String, Object>();
                    for (String f : fields) {
                        if (result.containsKey(f)) {
                            filtered.put(f, result.get(f));
                        }
                    }
                    // Only include accounts with active subscriptions
                    if (hasActiveSub) {
                        results.add(filtered);
                    }
                } else {
                    // Only include accounts with active subscriptions
                    if (hasActiveSub) {
                        results.add(result);
                    }
                }
            }
            
            // Return format based on whether fields are specified
            if (!fields.isEmpty()) {
                // Return just the filtered accounts array for PowerBI
                res.responseBody = Blob.valueOf(JSON.serialize(results));
            } else {
                // Return full structure with summary
                Map<String, Object> response = new Map<String, Object>{
                    'summary' => new Map<String, Object>{
                        'totalAccounts' => accounts.size(),
                        'totalSubscriptions' => totalSubscriptions,
                        'totalOpportunities' => totalOpportunities,
                        'totalARR' => totalARR.setScale(2),
                        'totalMRR' => totalMRR.setScale(2),
                        'totalACV' => totalACV.setScale(2),
                        'totalRevenue' => totalRevenue.setScale(2)
                    },
                    'accounts' => results,
                    'totalSize' => results.size(),
                    'done' => true
                };
                res.responseBody = Blob.valueOf(JSON.serialize(response));
            }
            res.statusCode = 200;
            
        } catch (Exception e) {
            res.statusCode = 500;
            res.responseBody = Blob.valueOf(JSON.serialize(new Map<String, String>{
                'error' => e.getMessage(), 
                'stackTrace' => e.getStackTraceString()
            }));
        }
    }
}