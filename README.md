# Salesforce Sales Metrics Automation

This Salesforce DX project contains automated sales metrics calculation and batch processing functionality for Account records. The system calculates comprehensive financial metrics including ARR, MRR, ACV, and other key performance indicators.

## Overview

The sales metrics system consists of several key components:

- **UpdateAccountSalesMetricsBatch**: Batch class that processes Account records and updates sales metrics fields
- **ClientSalesMetricsUtil**: Utility class that performs all sales metrics calculations
- **BatchLogAnalyzer**: Helper class for running and monitoring batch jobs
- **UpdateAccountSalesMetricsAction**: Invocable action for Flow/Process Builder integration

## Sales Metrics Calculations

### Monthly Recurring Revenue (MRR)

**Formula**: `Sum of (Subscription Product Total Price / Term in Months)`

**Calculation Logic**:
```apex
Decimal totalMRR = 0;
for (KGRenewal__SubscriptionProduct__c sp : subscriptionProducts) {
    if (sp.KGRenewal__TotalPrice__c != null && sp.KGRenewal__Term__c != null && sp.K<PERSON><PERSON>wal__Term__c > 0) {
        totalMRR += sp.KGR<PERSON>wal__TotalPrice__c / sp.KGRenewal__Term__c;
    }
}
```

**Data Sources**:
- `KGRenewal__SubscriptionProduct__c.KGRenewal__TotalPrice__c`
- `KGRenewal__SubscriptionProduct__c.KGRenewal__Term__c`

**Account Field**: `MRRc__c`

### Annual Recurring Revenue (ARR)

**Formula**: `MRR × 12`

**Calculation Logic**:
```apex
Decimal totalARR = totalMRR * 12;
```

**Account Field**: `ARRc__c`

### Annual Contract Value (ACV)

**Formula**:
- For 12-month terms: `Opportunity Amount`
- For other terms: `Opportunity Amount / (Term in Months / 12)`
- For date-based contracts: `Opportunity Amount / ((End Date - Start Date) / 365)`

**Calculation Logic**:
```apex
Decimal acv = 0;
if (opportunity.KGRenewal__Term__c == 12) {
    acv = opportunity.Amount;
} else if (opportunity.KGRenewal__Term__c > 0) {
    Decimal termYears = opportunity.KGRenewal__Term__c / 12;
    acv = opportunity.Amount / termYears;
} else if (contractEndDate != null && contractStartDate != null) {
    Decimal termYears = contractEndDate.daysBetween(contractStartDate) / 365;
    acv = opportunity.Amount / termYears;
}
```

**Conditions**: Only calculated for active contracts (Closed Won or Proforma Invoice opportunities)

**Account Field**: `Annual_Contract_Valuec__c`

### Subscription Metrics

#### Total Revenue
**Formula**: `Sum of all active subscription total amounts`
**Account Field**: `Total_Revenuec__c`

#### Renewal Amount
**Formula**: `Sum of all active subscription renewal amounts`
**Account Fields**:
- `Total_Renewal_Amountc__c`
- `Renewal_Amountc__c` (both fields populated with same value)

#### Subscription Count
**Formula**: `Count of active subscriptions`
**Account Field**: `Active_Subscription_Countc__c`

#### Renewal Count
**Formula**: `Count of active subscriptions (same as subscription count)`
**Account Field**: `Renewal_Countc__c`

#### Average Subscription Value
**Formula**: `Total Revenue / Subscription Count`
**Account Field**: `Average_Subscription_Valuec__c`

### Opportunity Metrics

#### Won Value
**Formula**: `Sum of Closed Won opportunity amounts`
**Account Field**: `Won_Valuec__c`

#### Lost Value
**Formula**: `Sum of Closed Lost opportunity amounts`
**Account Field**: `Lost_Valuec__c`

#### Negotiation Value
**Formula**: `Sum of open opportunity amounts (not Closed Won/Lost)`
**Account Field**: `Negotiation_Valuec__c`

#### Opportunity Counts
- **Total Opportunities**: `Opportunity_Countc__c`
- **Open Opportunities**: `Open_Opportunitiesc__c`
- **Lost Count**: `Lost_Countc__c`
- **Closed Won Count**: `Closed_Won_Countc__c`

### Contract Metrics

#### Closed Won ACV
**Formula**: ACV calculation applied to the latest Closed Won opportunity
**Account Field**: `Closed_Won_ACVc__c`

#### Targeted Construction Volume (TCV)
**Formula**: `Latest Closed Won opportunity's TCV field value`
**Account Field**: `Targeted_Construction_Volume_TCVc__c`

#### Contract Dates
- **Effective Date**: `Contract_Effective_Datec__c`
- **End Date**: `Contract_End_Datec__c`

### Invoice Metrics

#### Proforma Invoice Value
**Formula**: `Sum of Proforma Invoice opportunity amounts`
**Account Field**: `Proforma_Invoice_Valuec__c`

#### Proforma Invoice Count
**Formula**: `Count of Proforma Invoice opportunities`
**Account Field**: `Proforma_Invoice_Countc__c`

### Renewal Metrics

#### Open Renewals
**Formula**: `Count of active subscriptions with open renewal opportunities`
**Account Field**: `Open_Renewalsc__c`

## Batch Processing

### UpdateAccountSalesMetricsBatch

The batch class processes all Account records and updates their sales metrics fields based on related data.

**Execution**:
```apex
// Option 1: Simple execution
UpdateAccountSalesMetricsBatch batch = new UpdateAccountSalesMetricsBatch();
Database.executeBatch(batch, 200);

// Option 2: With tracking
String operationId = BatchLogAnalyzer.runAccountMetricsBatch(200);
```

**Features**:
- Processes accounts in configurable batch sizes (default: 200)
- Comprehensive logging with operation tracking
- Error handling and recovery
- Field-level change detection to avoid unnecessary updates
- Support for accounts with no related data (sets fields to zero)

### Field Mapping

The batch class maps calculated metrics to Account fields:

| Calculated Metric | Account Field | Data Type |
|-------------------|---------------|-----------|
| subscriptionCount | Active_Subscription_Countc__c | Number |
| totalRevenue | Total_Revenuec__c | Currency |
| totalRenewalAmount | Total_Renewal_Amountc__c | Currency |
| totalRenewalAmount | Renewal_Amountc__c | Currency |
| renewalCount | Renewal_Countc__c | Number |
| mrr | MRRc__c | Currency |
| arr | ARRc__c | Currency |
| annualContractValue | Annual_Contract_Valuec__c | Currency |
| wonValue | Won_Valuec__c | Currency |
| opportunityCount | Opportunity_Countc__c | Number |
| closedWonACV | Closed_Won_ACVc__c | Currency |
| openRenewals | Open_Renewalsc__c | Number |

## Data Sources

The calculations are based on the following Salesforce objects:

- **KGRenewal__Subscription__c**: Active subscriptions and renewal data
- **KGRenewal__SubscriptionProduct__c**: Product-level pricing for MRR/ARR
- **Opportunity**: Sales pipeline and closed deals
- **KGRenewal__Invoice__c**: Invoice and payment data

## Usage Examples

### Running the Batch Job
```apex
// Start batch with default settings
String operationId = BatchLogAnalyzer.runAccountMetricsBatch();

// Check batch status
Map<String, Object> summary = BatchLogAnalyzer.getRecentBatchSummary();
System.debug('Batch Status: ' + summary);
```

### Manual Calculation for Specific Accounts
```apex
List<Id> accountIds = new List<Id>{'001XXXXXXXXXXXXXXX'};
Map<Id, Map<String, Object>> metrics = ClientSalesMetricsUtil.calculateMetrics(accountIds);
```

### Flow Integration
The `UpdateAccountSalesMetricsAction` can be used in Flows and Process Builder to update metrics for specific accounts when triggered by record changes.

## Deployment

### Prerequisites
- Salesforce DX CLI
- Access to target org
- Required custom fields on Account object
- KGRenewal package installed

### Deploy Commands
```bash
# Deploy to sandbox
sf project deploy start --target-org your-sandbox

# Deploy to production (with tests)
sf project deploy start --target-org production --test-level RunLocalTests
```

### Test Coverage
The solution includes comprehensive test coverage:
- **UpdateAccountSalesMetricsBatch**: 94% coverage
- **ClientSalesMetricsUtil**: 85% coverage
- All tests pass with 100% success rate

## Monitoring and Logging

The system includes built-in logging through `KGRenewal__KineticGrowthLog__c` records:

- Batch execution start/completion
- Account-level processing details
- Error handling and recovery
- Performance metrics and timing

## Support

For questions or issues:
1. Check the batch logs in `KGRenewal__KineticGrowthLog__c`
2. Review test classes for usage examples
3. Monitor AsyncApexJob records for batch execution status

## Recent Updates

### Fixed Renewal Fields Issue
- **Issue**: `Renewal_Amountc__c` and `Renewal_Countc__c` fields were not being populated
- **Root Cause**: Missing field mapping in the batch class
- **Solution**: Added field mapping for `Renewal_Amountc__c` to receive the same value as `Total_Renewal_Amountc__c`
- **Impact**: Both renewal amount fields now consistently populated for Salesforce reports