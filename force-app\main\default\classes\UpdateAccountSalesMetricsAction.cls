public with sharing class UpdateAccountSalesMetricsAction {
    @InvocableMethod(label='Update Sales Metrics for Account' description='Updates sales metrics fields for the selected Account(s)')
    public static void updateMetrics(List<Id> accountIds) {
        Map<Id, Map<String, Object>> allMetrics = ClientSalesMetricsUtil.calculateMetrics(accountIds);
        List<Account> updates = new List<Account>();
        for (Id accId : accountIds) {
            Map<String, Object> metrics = allMetrics.get(accId);
            if (metrics != null) {
                Account acc = new Account(Id = accId);
                acc.Active_Subscription_Countc__c = (Integer)metrics.get('subscriptionCount');
                acc.Total_Revenuec__c = (Decimal)metrics.get('totalRevenue');
                acc.Total_Renewal_Amountc__c = (Decimal)metrics.get('totalRenewalAmount');
                acc.Total_Amount_Paidc__c = (Decimal)metrics.get('totalAmountPaid');
                acc.Total_Amount_Remainingc__c = (Decimal)metrics.get('totalAmountRemaining');
                acc.Average_Subscription_Valuec__c = (Decimal)metrics.get('averageSubscriptionValue');
                acc.Renewal_Datec__c = (Date)metrics.get('renewalDate');
                acc.Opportunity_Countc__c = (Integer)metrics.get('opportunityCount');
                acc.Open_Opportunitiesc__c = (Integer)metrics.get('openOpportunities');
                acc.Won_Valuec__c = (Decimal)metrics.get('wonValue');
                acc.Lost_Valuec__c = (Decimal)metrics.get('lostValue');
                acc.Lost_Countc__c = (Integer)metrics.get('lostCount');
                acc.Negotiation_Valuec__c = (Decimal)metrics.get('negotiationValue');
                acc.Annual_Contract_Valuec__c = (Decimal)metrics.get('annualContractValue');
                acc.MRRc__c = (Decimal)metrics.get('mrr');
                acc.ARRc__c = (Decimal)metrics.get('arr');
                acc.Targeted_Construction_Volume_TCVc__c = (Decimal)metrics.get('targetedConstructionVolumeTCV');
                acc.Contract_Effective_Datec__c = (Date)metrics.get('contractEffectiveDate');
                acc.Contract_End_Datec__c = (Date)metrics.get('contractEndDate');
                acc.Closed_Won_ACVc__c = (Decimal)metrics.get('closedWonACV');
                acc.Closed_Won_Countc__c = (Integer)metrics.get('closedWonCount');
                acc.Open_Renewalsc__c = (Integer)metrics.get('openRenewals');
                acc.Proforma_Invoice_Valuec__c = (Decimal)metrics.get('proformaInvoiceValue');
                acc.Proforma_Invoice_Countc__c = (Integer)metrics.get('proformaInvoiceCount');
                acc.Renewal_Countc__c = (Integer)metrics.get('renewalCount');
                updates.add(acc);
            }
        }
        if (!updates.isEmpty()) {
            update updates;
        }
    }
}