@isTest
public class ClientSalesControllerTest {
    static void setupTestData() {
        // Create Account
        Account acc = new Account(Name = 'Test Account');
        insert acc;

        // Create Opportunities
        Opportunity opp1 = new Opportunity(Name = 'Opp 1', AccountId = acc.Id, StageName = 'Closed Won', CloseDate = Date.today(), Amount = 1000, KGRenewal__Term__c = 12, KGRenewal__ContractEffectiveDate__c = Date.today().addMonths(-1), KGRenewal__ContractEndDate__c = Date.today().addMonths(11));
        Opportunity opp2 = new Opportunity(Name = 'Opp 2', AccountId = acc.Id, StageName = 'Proforma Invoice', CloseDate = Date.today(), Amount = 2000, KGRenewal__Term__c = 24, KGRenewal__ContractEffectiveDate__c = Date.today().addMonths(-6), K<PERSON><PERSON>wal__ContractEndDate__c = Date.today().addMonths(18));
        Opportunity opp3 = new Opportunity(Name = 'Opp 3', AccountId = acc.Id, StageName = 'Closed Lost', CloseDate = Date.today(), Amount = 500, KGRenewal__Term__c = 6, KGRenewal__ContractEffectiveDate__c = Date.today().addMonths(-3), KGRenewal__ContractEndDate__c = Date.today().addMonths(3));
        insert new List<Opportunity>{opp1, opp2, opp3};

        // Create Subscription
        KGRenewal__Subscription__c sub = new KGRenewal__Subscription__c(
            KGRenewal__Account__c = acc.Id,
            KGRenewal__TotalAmount__c = 3000,
            KGRenewal__RenewalAmount__c = 1000,
            KGRenewal__AmountPaid__c = 1500,
            KGRenewal__StartDate__c = Date.today().addMonths(-12),
            KGRenewal__EndDate__c = Date.today().addMonths(12)
        );
        insert sub;

        // Create Subscription Product
        KGRenewal__SubscriptionProduct__c prod = new KGRenewal__SubscriptionProduct__c(
            Name = 'Test Product',
            KGRenewal__Subscription__c = sub.Id,
            KGRenewal__TotalPrice__c = 1200,
            KGRenewal__Term__c = 12,
            KGRenewal__Quantity__c = 1,
            KGRenewal__UnitPrice__c = 1200
        );
        insert prod;
    }

    @isTest static void testGetClientSalesData() {
        setupTestData();
        Account acc = [SELECT Id FROM Account LIMIT 1];
        Test.startTest();
        Map<String, Object> data = ClientSalesController.getClientSalesData(acc.Id);
        System.assertNotEquals(null, data);
        Test.stopTest();
    }

    @isTest static void testGetOrgSalesDashboardData() {
        setupTestData();
        Test.startTest();
        Map<String, Object> data = ClientSalesController.getOrgSalesDashboardData();
        System.assertNotEquals(null, data);
        Test.stopTest();
    }

    @isTest static void testExportMethods() {
        setupTestData();
        Account acc = [SELECT Id FROM Account LIMIT 1];
        List<Id> ids = new List<Id>{acc.Id, null};
        Test.startTest();
        // Test all export methods with and without accountId
        ClientSalesController.exportSubscriptionData('TotalRevenue');
        ClientSalesController.exportSubscriptionData('RenewalAmount');
        ClientSalesController.exportSubscriptionData('AmountPaid');
        ClientSalesController.exportSubscriptionData('Other');
        ClientSalesController.exportActiveSubscriptions(acc.Id);
        ClientSalesController.exportActiveSubscriptions(null);
        ClientSalesController.exportActiveACVOpportunities(acc.Id);
        ClientSalesController.exportActiveACVOpportunities(null);
        ClientSalesController.exportActiveMRR(acc.Id);
        ClientSalesController.exportActiveMRR(null);
        ClientSalesController.exportActiveARR(acc.Id);
        ClientSalesController.exportActiveARR(null);
        ClientSalesController.exportTotalRevenue(acc.Id);
        ClientSalesController.exportTotalRevenue(null);
        ClientSalesController.exportRenewalAmount(acc.Id);
        ClientSalesController.exportRenewalAmount(null);
        ClientSalesController.exportAmountPaid(acc.Id);
        ClientSalesController.exportAmountPaid(null);
        ClientSalesController.exportAmountRemaining(acc.Id);
        ClientSalesController.exportAmountRemaining(null);
        ClientSalesController.exportOpenRenewals(acc.Id);
        ClientSalesController.exportOpenRenewals(null);
        ClientSalesController.exportTotalOpportunities(acc.Id);
        ClientSalesController.exportTotalOpportunities(null);
        ClientSalesController.exportOpenOpportunities(acc.Id);
        ClientSalesController.exportOpenOpportunities(null);
        ClientSalesController.exportWonValue(acc.Id);
        ClientSalesController.exportWonValue(null);
        ClientSalesController.exportNegotiationValue(acc.Id);
        ClientSalesController.exportNegotiationValue(null);
        ClientSalesController.exportLost(acc.Id);
        ClientSalesController.exportLost(null);
        ClientSalesController.exportTotalTenantId();
        ClientSalesController.exportProformaInvoice(acc.Id);
        ClientSalesController.exportProformaInvoice(null);
        ClientSalesController.exportClosedWonACV(acc.Id);
        ClientSalesController.exportClosedWonACV(null);
        Test.stopTest();
    }

    @isTest static void testGetClientType() {
        setupTestData();
        Account acc = [SELECT Id FROM Account LIMIT 1];
        Test.startTest();
        String type = ClientSalesController.getClientType(acc.Id);
        System.assertNotEquals(null, type);
        String typeNull = ClientSalesController.getClientType(null);
        System.assertEquals('Standard Client', typeNull);
        Test.stopTest();
    }

    @isTest static void testClientSalesMetricsUtil() {
        setupTestData();
        Account acc = [SELECT Id FROM Account LIMIT 1];
        Test.startTest();
        Map<Id, Map<String, Object>> metrics = ClientSalesMetricsUtil.calculateMetrics(new List<Id>{acc.Id});
        System.assert(metrics.containsKey(acc.Id));
        // Test with empty list
        Map<Id, Map<String, Object>> emptyMetrics = ClientSalesMetricsUtil.calculateMetrics(new List<Id>());
        System.assertEquals(0, emptyMetrics.size());
        Test.stopTest();
    }
}