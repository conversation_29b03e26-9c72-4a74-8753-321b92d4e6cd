// Test script to verify that both Renewal_Amountc__c and Renewal_Countc__c fields are being populated

System.debug('=== TESTING RENEWAL FIELDS POPULATION ===');

// Create a test account
Account testAccount = new Account(
    Name = 'Renewal Fields Test Account',
    Tenant_ID__c = 99999,
    // Set initial values to verify they get updated
    Total_Renewal_Amountc__c = 0,
    Renewal_Amountc__c = 0,
    Renewal_Countc__c = 0
);
insert testAccount;

// Create subscription with renewal amount
KGRenewal__Subscription__c sub = new KGRenewal__Subscription__c(
    KGRenewal__Account__c = testAccount.Id,
    KGRenewal__TotalAmount__c = 15000,
    KGRenewal__RenewalAmount__c = 12000,  // This should populate both renewal fields
    KGRenewal__StartDate__c = Date.today(),
    KGRenewal__EndDate__c = Date.today().addDays(365)
);
insert sub;

System.debug('Created test account: ' + testAccount.Id);
System.debug('Created test subscription with renewal amount: 12000');

// Check values before batch execution
Account beforeBatch = [SELECT Id, Name, Total_Renewal_Amountc__c, Renewal_Amountc__c, Renewal_Countc__c 
                      FROM Account WHERE Id = :testAccount.Id];

System.debug('\n=== BEFORE BATCH EXECUTION ===');
System.debug('Total_Renewal_Amountc__c: ' + beforeBatch.Total_Renewal_Amountc__c);
System.debug('Renewal_Amountc__c: ' + beforeBatch.Renewal_Amountc__c);
System.debug('Renewal_Countc__c: ' + beforeBatch.Renewal_Countc__c);

// Test ClientSalesMetricsUtil directly
System.debug('\n=== TESTING ClientSalesMetricsUtil ===');
Map<Id, Map<String, Object>> metrics = ClientSalesMetricsUtil.calculateMetrics(new List<Id>{testAccount.Id});
Map<String, Object> accountMetrics = metrics.get(testAccount.Id);

if (accountMetrics != null) {
    System.debug('totalRenewalAmount from util: ' + accountMetrics.get('totalRenewalAmount'));
    System.debug('renewalCount from util: ' + accountMetrics.get('renewalCount'));
} else {
    System.debug('No metrics returned from ClientSalesMetricsUtil');
}

// Execute the batch manually
System.debug('\n=== EXECUTING BATCH ===');
UpdateAccountSalesMetricsBatch batch = new UpdateAccountSalesMetricsBatch();

// Get the account with all fields for batch processing
List<Account> testScope = [SELECT Id, Total_Renewal_Amountc__c, Renewal_Amountc__c, Renewal_Countc__c,
                                 Active_Subscription_Countc__c, Total_Revenuec__c, Total_Amount_Paidc__c,
                                 Total_Amount_Remainingc__c, Average_Subscription_Valuec__c, Renewal_Datec__c, Opportunity_Countc__c,
                                 Open_Opportunitiesc__c, Won_Valuec__c, Lost_Valuec__c, Lost_Countc__c, Negotiation_Valuec__c,
                                 Annual_Contract_Valuec__c, MRRc__c, ARRc__c, Targeted_Construction_Volume_TCVc__c,
                                 Contract_Effective_Datec__c, Contract_End_Datec__c, Closed_Won_ACVc__c, Closed_Won_Countc__c,
                                 Open_Renewalsc__c, Proforma_Invoice_Valuec__c, Proforma_Invoice_Countc__c
                          FROM Account WHERE Id = :testAccount.Id];

// Execute the batch using Database.executeBatch
Id jobId = Database.executeBatch(batch, 1);
System.debug('Batch job started with ID: ' + jobId);

// Wait a moment for batch to complete (in real scenario, you'd check AsyncApexJob status)
// For this test, we'll just proceed and check the results

// Check values after batch execution
Account afterBatch = [SELECT Id, Name, Total_Renewal_Amountc__c, Renewal_Amountc__c, Renewal_Countc__c
                     FROM Account WHERE Id = :testAccount.Id];

System.debug('\n=== AFTER BATCH EXECUTION ===');
System.debug('Total_Renewal_Amountc__c: ' + afterBatch.Total_Renewal_Amountc__c);
System.debug('Renewal_Amountc__c: ' + afterBatch.Renewal_Amountc__c);
System.debug('Renewal_Countc__c: ' + afterBatch.Renewal_Countc__c);

// Verify the results
System.debug('\n=== VERIFICATION ===');
Boolean totalRenewalCorrect = afterBatch.Total_Renewal_Amountc__c == 12000;
Boolean renewalAmountCorrect = afterBatch.Renewal_Amountc__c == 12000;
Boolean renewalCountCorrect = afterBatch.Renewal_Countc__c == 1;

System.debug('✅ Total_Renewal_Amountc__c populated correctly: ' + totalRenewalCorrect);
System.debug('✅ Renewal_Amountc__c populated correctly: ' + renewalAmountCorrect);
System.debug('✅ Renewal_Countc__c populated correctly: ' + renewalCountCorrect);

if (totalRenewalCorrect && renewalAmountCorrect && renewalCountCorrect) {
    System.debug('\n🎉 SUCCESS: All renewal fields are being populated correctly!');
} else {
    System.debug('\n❌ ISSUE: Some renewal fields are not being populated correctly.');
}

// Check batch logs (note: logs may not be available immediately after executeBatch)
System.debug('\n=== BATCH EXECUTION INITIATED ===');
System.debug('Batch job ID: ' + jobId);
System.debug('Note: In production, you would monitor the AsyncApexJob status to confirm completion.');

// Clean up test data
delete sub;
delete testAccount;
System.debug('\nTest data cleaned up.');
