// Test script to investigate renewal amount and renewal count issues

// Get a sample account with subscriptions
List<Account> accounts = [SELECT Id, Name, Total_Renewal_Amountc__c, Renewal_Countc__c 
                         FROM Account 
                         WHERE Id IN (SELECT KGRenewal__Account__c FROM KGRenewal__Subscription__c WHERE KGRenewal__IsActiveTerm__c = true)
                         LIMIT 3];

System.debug('=== ACCOUNT FIELD VALUES BEFORE BATCH ===');
for (Account acc : accounts) {
    System.debug('Account: ' + acc.Name + ' (ID: ' + acc.Id + ')');
    System.debug('  Total_Renewal_Amountc__c: ' + acc.Total_Renewal_Amountc__c);
    System.debug('  Renewal_Countc__c: ' + acc.Renewal_Countc__c);
}

// Test ClientSalesMetricsUtil calculation
List<Id> accountIds = new List<Id>();
for (Account acc : accounts) {
    accountIds.add(acc.Id);
}

System.debug('\n=== TESTING ClientSalesMetricsUtil.calculateMetrics ===');
Map<Id, Map<String, Object>> allMetrics = ClientSalesMetricsUtil.calculateMetrics(accountIds);

for (Id accId : accountIds) {
    Map<String, Object> metrics = allMetrics.get(accId);
    System.debug('Account ID: ' + accId);
    if (metrics != null) {
        System.debug('  totalRenewalAmount: ' + metrics.get('totalRenewalAmount'));
        System.debug('  renewalCount: ' + metrics.get('renewalCount'));
        System.debug('  subscriptionCount: ' + metrics.get('subscriptionCount'));
        System.debug('  All metrics keys: ' + metrics.keySet());
    } else {
        System.debug('  No metrics returned for this account');
    }
}

// Check subscription data for these accounts
System.debug('\n=== SUBSCRIPTION DATA FOR THESE ACCOUNTS ===');
List<KGRenewal__Subscription__c> subs = [SELECT Id, KGRenewal__Account__c, KGRenewal__RenewalAmount__c, KGRenewal__IsActiveTerm__c
                                        FROM KGRenewal__Subscription__c 
                                        WHERE KGRenewal__Account__c IN :accountIds
                                        AND KGRenewal__IsActiveTerm__c = true];

Map<Id, List<KGRenewal__Subscription__c>> subsByAccount = new Map<Id, List<KGRenewal__Subscription__c>>();
for (KGRenewal__Subscription__c sub : subs) {
    if (!subsByAccount.containsKey(sub.KGRenewal__Account__c)) {
        subsByAccount.put(sub.KGRenewal__Account__c, new List<KGRenewal__Subscription__c>());
    }
    subsByAccount.get(sub.KGRenewal__Account__c).add(sub);
}

for (Id accId : accountIds) {
    List<KGRenewal__Subscription__c> accountSubs = subsByAccount.get(accId);
    System.debug('Account ' + accId + ' has ' + (accountSubs != null ? accountSubs.size() : 0) + ' active subscriptions');
    if (accountSubs != null) {
        Decimal totalRenewalAmount = 0;
        for (KGRenewal__Subscription__c sub : accountSubs) {
            System.debug('  Subscription ' + sub.Id + ' - RenewalAmount: ' + sub.KGRenewal__RenewalAmount__c);
            totalRenewalAmount += (sub.KGRenewal__RenewalAmount__c != null ? sub.KGRenewal__RenewalAmount__c : 0);
        }
        System.debug('  Total calculated renewal amount: ' + totalRenewalAmount);
        System.debug('  Total subscription count: ' + accountSubs.size());
    }
}
