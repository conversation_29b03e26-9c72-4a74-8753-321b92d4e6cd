public without sharing class <PERSON><PERSON><PERSON>ogAnalyzer {
    
    /**
     * Get all batch operations that have run
     */
    public static List<String> getBatchOperations() {
        List<String> operations = new List<String>();
        for (AggregateResult ar : [SELECT KGRenewal__OperationId__c
                                  FROM KGRenewal__KineticGrowthLog__c 
                                  WHERE KGRenewal__OperationId__c LIKE 'BATCH-%'
                                  GROUP BY KGRenewal__OperationId__c
                                  ORDER BY MAX(CreatedDate) DESC
                                  LIMIT 50]) {
            operations.add((String)ar.get('KGRenewal__OperationId__c'));
        }
        return operations;
    }
    
    /**
     * Get detailed logs for a specific batch operation
     */
    public static List<KGRenewal__KineticGrowthLog__c> getBatchLogs(String batchOperationId) {
        return [SELECT Id, Name, KGRenewal__OperationId__c, KGRenewal__Details__c, 
                KGRenewal__IsSuccess__c, <PERSON><PERSON><PERSON>wal__Timestamp__c, CreatedDate
                FROM KGRenewal__KineticGrowthLog__c 
                WHERE KGRenewal__OperationId__c = :batchOperationId
                ORDER BY CreatedDate];
    }
    
    /**
     * Get summary of the most recent batch operation
     */
    public static Map<String, Object> getRecentBatchSummary() {
        List<String> operations = getBatchOperations();
        if (operations.isEmpty()) {
            return new Map<String, Object>{'error' => 'No batch operations found'};
        }
        
        String recentOperation = operations[0];
        List<KGRenewal__KineticGrowthLog__c> logs = getBatchLogs(recentOperation);
        
        Map<String, Object> summary = new Map<String, Object>();
        summary.put('operationId', recentOperation);
        summary.put('totalLogs', logs.size());
        summary.put('startTime', null);
        summary.put('endTime', null);
        summary.put('success', true);
        summary.put('accountsUpdated', 0);
        summary.put('accountsSkipped', 0);
        summary.put('errors', 0);
        summary.put('chunksProcessed', 0);
        
        Integer accountsUpdated = 0;
        Integer accountsSkipped = 0;
        Integer errors = 0;
        Integer chunksProcessed = 0;
        
        for (KGRenewal__KineticGrowthLog__c log : logs) {
            // Track start and end times
            if (log.Name.contains('Batch Started')) {
                summary.put('startTime', log.CreatedDate);
            }
            if (log.Name.contains('Batch Completed')) {
                summary.put('endTime', log.CreatedDate);
                summary.put('success', log.KGRenewal__IsSuccess__c);
            }
            
            // Count different types of operations
            if (log.Name.contains('Account Metrics Updated')) {
                accountsUpdated++;
            } else if (log.Name.contains('No Changes Required')) {
                accountsSkipped++;
            } else if (log.Name.contains('Processing Account Chunk')) {
                chunksProcessed++;
            } else if (!log.KGRenewal__IsSuccess__c) {
                errors++;
            }
        }
        
        summary.put('accountsUpdated', accountsUpdated);
        summary.put('accountsSkipped', accountsSkipped);
        summary.put('errors', errors);
        summary.put('chunksProcessed', chunksProcessed);
        
        return summary;
    }
    
    /**
     * Get accounts that were updated in a specific batch operation
     */
    public static List<Map<String, Object>> getUpdatedAccounts(String batchOperationId) {
        List<Map<String, Object>> updatedAccounts = new List<Map<String, Object>>();
        
        List<KGRenewal__KineticGrowthLog__c> logs = [SELECT KGRenewal__Details__c, CreatedDate
                                                   FROM KGRenewal__KineticGrowthLog__c 
                                                   WHERE KGRenewal__OperationId__c = :batchOperationId
                                                   AND Name = 'Account Metrics Updated'
                                                   ORDER BY CreatedDate];
        
        for (KGRenewal__KineticGrowthLog__c log : logs) {
            Map<String, Object> accountInfo = parseAccountUpdateLog(log.KGRenewal__Details__c);
            accountInfo.put('updatedAt', log.CreatedDate);
            updatedAccounts.add(accountInfo);
        }
        
        return updatedAccounts;
    }
    
    /**
     * Get field change statistics for a batch operation
     */
    public static Map<String, Integer> getFieldChangeStats(String batchOperationId) {
        Map<String, Integer> fieldStats = new Map<String, Integer>();
        
        List<KGRenewal__KineticGrowthLog__c> logs = [SELECT KGRenewal__Details__c
                                                   FROM KGRenewal__KineticGrowthLog__c 
                                                   WHERE KGRenewal__OperationId__c = :batchOperationId
                                                   AND Name = 'Account Metrics Updated'];
        
        for (KGRenewal__KineticGrowthLog__c log : logs) {
            List<String> changedFields = parseChangedFields(log.KGRenewal__Details__c);
            for (String field : changedFields) {
                Integer count = fieldStats.get(field);
                fieldStats.put(field, (count == null ? 1 : count + 1));
            }
        }
        
        return fieldStats;
    }
    
    /**
     * Get batch operations with errors
     */
    public static List<Map<String, Object>> getBatchesWithErrors() {
        List<Map<String, Object>> errorBatches = new List<Map<String, Object>>();
        
        List<AggregateResult> results = [SELECT KGRenewal__OperationId__c, COUNT(Id) errorCount
                                       FROM KGRenewal__KineticGrowthLog__c 
                                       WHERE KGRenewal__OperationId__c LIKE 'BATCH-%'
                                       AND KGRenewal__IsSuccess__c = false
                                       GROUP BY KGRenewal__OperationId__c
                                       ORDER BY MAX(CreatedDate) DESC];
        
        for (AggregateResult ar : results) {
            Map<String, Object> errorBatch = new Map<String, Object>();
            errorBatch.put('operationId', ar.get('KGRenewal__OperationId__c'));
            errorBatch.put('errorCount', ar.get('errorCount'));
            errorBatches.add(errorBatch);
        }
        
        return errorBatches;
    }
    
    /**
     * Helper method to parse account update log details
     */
    private static Map<String, Object> parseAccountUpdateLog(String details) {
        Map<String, Object> accountInfo = new Map<String, Object>();
        
        if (String.isBlank(details)) {
            return accountInfo;
        }
        
        // Extract account name and ID
        if (details.contains('Account: ')) {
            String accountLine = details.substring(details.indexOf('Account: ') + 9);
            if (accountLine.contains('\n')) {
                accountLine = accountLine.substring(0, accountLine.indexOf('\n'));
            }
            
            if (accountLine.contains(' (') && accountLine.contains(')')) {
                String accountName = accountLine.substring(0, accountLine.indexOf(' ('));
                String accountId = accountLine.substring(accountLine.indexOf('(') + 1, accountLine.indexOf(')'));
                accountInfo.put('accountName', accountName);
                accountInfo.put('accountId', accountId);
            }
        }
        
        // Extract changed fields count
        if (details.contains('Changed Fields: ')) {
            String fieldsLine = details.substring(details.indexOf('Changed Fields: ') + 16);
            if (fieldsLine.contains('\n')) {
                fieldsLine = fieldsLine.substring(0, fieldsLine.indexOf('\n'));
            }
            String[] fields = fieldsLine.split(', ');
            accountInfo.put('changedFieldsCount', fields.size());
            accountInfo.put('changedFields', fields);
        }
        
        return accountInfo;
    }
    
    /**
     * Helper method to parse changed fields from log details
     */
    private static List<String> parseChangedFields(String details) {
        List<String> fields = new List<String>();
        
        if (String.isBlank(details) || !details.contains('Changed Fields: ')) {
            return fields;
        }
        
        String fieldsLine = details.substring(details.indexOf('Changed Fields: ') + 16);
        if (fieldsLine.contains('\n')) {
            fieldsLine = fieldsLine.substring(0, fieldsLine.indexOf('\n'));
        }
        
        if (!String.isBlank(fieldsLine)) {
            fields.addAll(fieldsLine.split(', '));
        }
        
        return fields;
    }
    
    /**
     * Run a batch operation and return the operation ID for tracking
     */
    public static String runAccountMetricsBatch(Integer batchSize) {
        if (batchSize == null || batchSize <= 0) {
            batchSize = 200;
        }
        
        UpdateAccountSalesMetricsBatch batch = new UpdateAccountSalesMetricsBatch();
        Id jobId = Database.executeBatch(batch, batchSize);
        
        // Return the actual operation ID from the batch instance
        return batch.getOperationId();
    }
}