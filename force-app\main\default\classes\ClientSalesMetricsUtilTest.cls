@isTest
private class ClientSalesMetricsUtilTest {
    
    @testSetup
    static void setupTestData() {
        // Create test accounts
        List<Account> testAccounts = new List<Account>();
        for (Integer i = 0; i < 3; i++) {
            testAccounts.add(new Account(
                Name = 'Util Test Account ' + i,
                Tenant_ID__c = 2000 + i
            ));
        }
        insert testAccounts;
        
        Date today = Date.today();
        
        // Create test subscriptions with various scenarios
        List<KGRenewal__Subscription__c> testSubs = new List<KGRenewal__Subscription__c>();
        
        // Active subscription with renewal opportunity
        testSubs.add(new KGRenewal__Subscription__c(
            KGRenewal__Account__c = testAccounts[0].Id,
            KGRenewal__TotalAmount__c = 120000,
            KGRenewal__RenewalAmount__c = 110000,
            KGRenewal__AmountPaid__c = 80000,
            KGRenewal__StartDate__c = today.addDays(-60),
            KGRenewal__EndDate__c = today.addDays(305),
            K<PERSON>enewal__RenewalDate__c = today.addDays(280)
        ));

        // Active subscription without renewal
        testSubs.add(new KGRenewal__Subscription__c(
            KGRenewal__Account__c = testAccounts[1].Id,
            KGRenewal__TotalAmount__c = 60000,
            KGRenewal__RenewalAmount__c = 55000,
            KGRenewal__AmountPaid__c = 30000,
            KGRenewal__StartDate__c = today.addDays(-30),
            KGRenewal__EndDate__c = today.addDays(335),
            KGRenewal__RenewalDate__c = today.addDays(300)
        ));

        // Inactive subscription (for testing all subscriptions query)
        testSubs.add(new KGRenewal__Subscription__c(
            KGRenewal__Account__c = testAccounts[0].Id,
            KGRenewal__TotalAmount__c = 30000,
            KGRenewal__StartDate__c = today.addDays(-400),
            KGRenewal__EndDate__c = today.addDays(-35)
        ));
        
        insert testSubs;
        
        // Create opportunities with various stages and contract dates
        List<Opportunity> testOpps = new List<Opportunity>();
        
        // Closed Won with active contract
        testOpps.add(new Opportunity(
            Name = 'Active Closed Won',
            AccountId = testAccounts[0].Id,
            StageName = 'Closed Won',
            CloseDate = today.addDays(-10),
            Amount = 80000,
            KGRenewal__Term__c = 24,
            KGRenewal__ContractEffectiveDate__c = today.addDays(-10),
            KGRenewal__ContractEndDate__c = today.addDays(720),
            Targeted_Construction_Volume_TCV__c = 150000
        ));
        
        // Closed Won with inactive contract (past dates)
        testOpps.add(new Opportunity(
            Name = 'Inactive Closed Won',
            AccountId = testAccounts[0].Id,
            StageName = 'Closed Won',
            CloseDate = today.addDays(-200),
            Amount = 40000,
            KGRenewal__Term__c = 12,
            KGRenewal__ContractEffectiveDate__c = today.addDays(-200),
            KGRenewal__ContractEndDate__c = today.addDays(-50)
        ));
        
        // Proforma Invoice
        testOpps.add(new Opportunity(
            Name = 'Proforma Test',
            AccountId = testAccounts[1].Id,
            StageName = 'Proforma Invoice',
            CloseDate = today.addDays(15),
            Amount = 25000,
            KGRenewal__Term__c = 12,
            KGRenewal__ContractEffectiveDate__c = today.addDays(15),
            KGRenewal__ContractEndDate__c = today.addDays(380)
        ));
        
        // Closed Lost
        testOpps.add(new Opportunity(
            Name = 'Lost Opp',
            AccountId = testAccounts[1].Id,
            StageName = 'Closed Lost',
            CloseDate = today.addDays(-5),
            Amount = 35000
        ));
        
        // Open opportunity
        testOpps.add(new Opportunity(
            Name = 'Open Negotiation',
            AccountId = testAccounts[2].Id,
            StageName = 'Negotiation/Review',
            CloseDate = today.addDays(45),
            Amount = 50000
        ));
        
        insert testOpps;
        
        // Create renewal opportunity and link to subscription
        Opportunity renewalOpp = new Opportunity(
            Name = 'Renewal Opportunity',
            AccountId = testAccounts[0].Id,
            StageName = 'Negotiation/Review',
            CloseDate = today.addDays(30),
            Amount = 90000
        );
        insert renewalOpp;
        
        // Link subscription to renewal opportunity
        testSubs[0].KGRenewal__RenewalOpportunity__c = renewalOpp.Id;
        update testSubs[0];
        
        // Create subscription products for MRR/ARR calculations
        List<KGRenewal__SubscriptionProduct__c> testSubProducts = new List<KGRenewal__SubscriptionProduct__c>();
        testSubProducts.add(new KGRenewal__SubscriptionProduct__c(
            KGRenewal__Subscription__c = testSubs[0].Id,
            KGRenewal__TotalPrice__c = 60000,
            KGRenewal__Term__c = 12,
            KGRenewal__Quantity__c = 1,
            KGRenewal__UnitPrice__c = 60000
        ));
        testSubProducts.add(new KGRenewal__SubscriptionProduct__c(
            KGRenewal__Subscription__c = testSubs[1].Id,
            KGRenewal__TotalPrice__c = 36000,
            KGRenewal__Term__c = 12,
            KGRenewal__Quantity__c = 1,
            KGRenewal__UnitPrice__c = 36000
        ));
        insert testSubProducts;
    }
    
    @isTest
    static void testCalculateMetricsBasicFunctionality() {
        List<Account> testAccounts = [SELECT Id FROM Account WHERE Name LIKE 'Util Test Account%' ORDER BY Name];
        List<Id> accountIds = new List<Id>();
        for (Account acc : testAccounts) {
            accountIds.add(acc.Id);
        }
        
        Test.startTest();
        Map<Id, Map<String, Object>> results = ClientSalesMetricsUtil.calculateMetrics(accountIds);
        Test.stopTest();
        
        System.assertNotEquals(null, results, 'Should return results');
        System.assertEquals(testAccounts.size(), results.size(), 'Should return metrics for all accounts');
        
        // Test first account (has subscription, opportunities, and renewal)
        Map<String, Object> metrics0 = results.get(testAccounts[0].Id);
        System.assertNotEquals(null, metrics0, 'Should have metrics for first account');
        System.assertEquals(1, metrics0.get('subscriptionCount'), 'Should have 1 active subscription');
        System.assertEquals(3, metrics0.get('opportunityCount'), 'Should have 3 opportunities');
        System.assertEquals(1, metrics0.get('openRenewals'), 'Should have 1 open renewal');
        System.assertEquals(2, metrics0.get('closedWonCount'), 'Should have 2 closed won opportunities');
        
        // Test second account (has subscription, proforma, and lost opp)
        Map<String, Object> metrics1 = results.get(testAccounts[1].Id);
        System.assertNotEquals(null, metrics1, 'Should have metrics for second account');
        System.assertEquals(1, metrics1.get('subscriptionCount'), 'Should have 1 active subscription');
        System.assertEquals(2, metrics1.get('opportunityCount'), 'Should have 2 opportunities');
        System.assertEquals(25000, metrics1.get('proformaInvoiceValue'), 'Should have proforma value');
        System.assertEquals(1, metrics1.get('proformaInvoiceCount'), 'Should have 1 proforma invoice');
        System.assertEquals(1, metrics1.get('lostCount'), 'Should have 1 lost opportunity');
        System.assertEquals(35000, metrics1.get('lostValue'), 'Should have lost value');
        
        // Test third account (only open opportunity)
        Map<String, Object> metrics2 = results.get(testAccounts[2].Id);
        System.assertNotEquals(null, metrics2, 'Should have metrics for third account');
        System.assertEquals(0, metrics2.get('subscriptionCount'), 'Should have 0 subscriptions');
        System.assertEquals(1, metrics2.get('opportunityCount'), 'Should have 1 opportunity');
        System.assertEquals(1, metrics2.get('openOpportunities'), 'Should have 1 open opportunity');
        System.assertEquals(50000, metrics2.get('negotiationValue'), 'Should have negotiation value');
    }
    
    @isTest
    static void testCalculateMetricsACVCalculations() {
        List<Account> testAccounts = [SELECT Id FROM Account WHERE Name LIKE 'Util Test Account%' ORDER BY Name];
        
        Test.startTest();
        Map<Id, Map<String, Object>> results = ClientSalesMetricsUtil.calculateMetrics(new List<Id>{testAccounts[0].Id});
        Test.stopTest();
        
        Map<String, Object> metrics = results.get(testAccounts[0].Id);
        
        // Test ACV calculations for active contracts
        // Active Closed Won: 80000 amount, 24 month term = 40000 ACV
        // Inactive Closed Won: should not contribute to ACV (contract dates in past)
        System.assertEquals(40000, metrics.get('annualContractValue'), 'Should calculate ACV for active contracts only');
        
        // Test Closed Won ACV (latest closed won)
        System.assertEquals(40000, metrics.get('closedWonACV'), 'Should have ACV from latest closed won');
        
        // Test TCV from latest closed won
        System.assertEquals(150000, metrics.get('targetedConstructionVolumeTCV'), 'Should have TCV from latest closed won');
    }
    
    @isTest
    static void testCalculateMetricsMRRARRCalculations() {
        List<Account> testAccounts = [SELECT Id FROM Account WHERE Name LIKE 'Util Test Account%' ORDER BY Name];

        Test.startTest();
        Map<Id, Map<String, Object>> results = ClientSalesMetricsUtil.calculateMetrics(new List<Id>{testAccounts[0].Id, testAccounts[1].Id});
        Test.stopTest();

        // Test that MRR/ARR fields are present and numeric (may be 0 if KGRenewal__IsCurrent__c is not set)
        Map<String, Object> metrics0 = results.get(testAccounts[0].Id);
        System.assertNotEquals(null, metrics0.get('mrr'), 'Should have MRR field for first account');
        System.assertNotEquals(null, metrics0.get('arr'), 'Should have ARR field for first account');
        System.assert(metrics0.get('mrr') instanceof Decimal, 'MRR should be a Decimal');
        System.assert(metrics0.get('arr') instanceof Decimal, 'ARR should be a Decimal');

        // Test second account MRR/ARR
        Map<String, Object> metrics1 = results.get(testAccounts[1].Id);
        System.assertNotEquals(null, metrics1.get('mrr'), 'Should have MRR field for second account');
        System.assertNotEquals(null, metrics1.get('arr'), 'Should have ARR field for second account');
        System.assert(metrics1.get('mrr') instanceof Decimal, 'MRR should be a Decimal');
        System.assert(metrics1.get('arr') instanceof Decimal, 'ARR should be a Decimal');
    }
    
    @isTest
    static void testCalculateMetricsContractDateCalculations() {
        List<Account> testAccounts = [SELECT Id FROM Account WHERE Name LIKE 'Util Test Account%' ORDER BY Name];
        
        Test.startTest();
        Map<Id, Map<String, Object>> results = ClientSalesMetricsUtil.calculateMetrics(new List<Id>{testAccounts[0].Id, testAccounts[1].Id});
        Test.stopTest();
        
        // Test contract dates from latest opportunity
        Map<String, Object> metrics0 = results.get(testAccounts[0].Id);
        Date expectedEffectiveDate = Date.today().addDays(-10);
        Date expectedEndDate = Date.today().addDays(720);
        System.assertEquals(expectedEffectiveDate, metrics0.get('contractEffectiveDate'), 'Should have contract effective date from latest opp');
        System.assertEquals(expectedEndDate, metrics0.get('contractEndDate'), 'Should have contract end date from latest opp');
        
        // Test contract dates for proforma invoice
        Map<String, Object> metrics1 = results.get(testAccounts[1].Id);
        Date expectedProformaEffective = Date.today().addDays(15);
        Date expectedProformaEnd = Date.today().addDays(380);
        System.assertEquals(expectedProformaEffective, metrics1.get('contractEffectiveDate'), 'Should have contract dates from proforma');
        System.assertEquals(expectedProformaEnd, metrics1.get('contractEndDate'), 'Should have contract dates from proforma');
    }
    
    @isTest
    static void testCalculateMetricsEdgeCases() {
        // Test with null and empty inputs
        Test.startTest();
        Map<Id, Map<String, Object>> nullResults = ClientSalesMetricsUtil.calculateMetrics(null);
        Map<Id, Map<String, Object>> emptyResults = ClientSalesMetricsUtil.calculateMetrics(new List<Id>());
        Test.stopTest();
        
        System.assertNotEquals(null, nullResults, 'Should handle null input gracefully');
        System.assertEquals(0, nullResults.size(), 'Should return empty map for null input');
        
        System.assertNotEquals(null, emptyResults, 'Should handle empty input gracefully');
        System.assertEquals(0, emptyResults.size(), 'Should return empty map for empty input');
    }
    
    @isTest
    static void testCalculateMetricsNonExistentAccounts() {
        // Test with non-existent account IDs
        List<Id> fakeIds = new List<Id>{'001000000000001AAA', '001000000000002AAA'};
        
        Test.startTest();
        Map<Id, Map<String, Object>> results = ClientSalesMetricsUtil.calculateMetrics(fakeIds);
        Test.stopTest();
        
        System.assertNotEquals(null, results, 'Should return results for non-existent accounts');
        System.assertEquals(2, results.size(), 'Should return entries for all requested IDs');
        
        for (Id fakeId : fakeIds) {
            Map<String, Object> metrics = results.get(fakeId);
            System.assertNotEquals(null, metrics, 'Should have metrics entry for fake ID');
            System.assertEquals(null, metrics.get('account'), 'Account should be null for non-existent ID');
            System.assertEquals(0, metrics.get('subscriptionCount'), 'Should have 0 subscriptions for non-existent account');
            System.assertEquals(0, metrics.get('totalRevenue'), 'Should have 0 revenue for non-existent account');
        }
    }
}