@isTest
private class UpdateAccountSalesMetricsBatchLogTest {
    
    @testSetup
    static void setup() {
        // Create test account
        Account testAccount = new Account(
            Name = 'Test Logging Account',
            Tenant_ID__c = 9999
        );
        insert testAccount;
        
        // Create test opportunity
        Opportunity testOpp = new Opportunity(
            Name = 'Test Logging Opp',
            AccountId = testAccount.Id,
            StageName = 'Closed Won',
            CloseDate = Date.today(),
            Amount = 5000
        );
        insert testOpp;
    }
    
    @isTest
    static void testBatchLogging() {
        Test.startTest();
        UpdateAccountSalesMetricsBatch batch = new UpdateAccountSalesMetricsBatch();
        String operationId = batch.getOperationId();
        Database.executeBatch(batch, 1);
        Test.stopTest();
        
        // Verify logs were created
        List<KGRenewal__KineticGrowthLog__c> logs = [
            SELECT Name, KGRenewal__OperationId__c, KGRenewal__IsSuccess__c, KGRenewal__Details__c
            FROM K<PERSON>wal__KineticGrowthLog__c 
            WHERE K<PERSON>__OperationId__c = :operationId
            ORDER BY CreatedDate
        ];
        
        System.assert(!logs.isEmpty(), 'Logs should be created');
        
        // Check for batch start log
        Boolean foundStartLog = false;
        Boolean foundUpdateLog = false;
        Boolean foundCompleteLog = false;
        
        for (KGRenewal__KineticGrowthLog__c log : logs) {
            if (log.Name == 'Batch Started') foundStartLog = true;
            if (log.Name == 'Account Metrics Updated') foundUpdateLog = true;
            if (log.Name == 'Batch Completed') foundCompleteLog = true;
        }
        
        System.assert(foundStartLog, 'Should have batch start log');
        System.assert(foundCompleteLog, 'Should have batch completion log');
    }
    
    @isTest
    static void testBatchLogAnalyzerIntegration() {
        // Test the batch creation and operation ID generation without executing
        UpdateAccountSalesMetricsBatch batch = new UpdateAccountSalesMetricsBatch();
        String operationId = batch.getOperationId();

        System.assertNotEquals(null, operationId, 'Operation ID should be returned');
        System.assert(operationId.startsWith('B'), 'Operation ID should have correct format');

        // Create a test log to verify retrieval functionality
        KGRenewal__KineticGrowthLog__c testLog = new KGRenewal__KineticGrowthLog__c(
            Name = 'Test Log',
            KGRenewal__OperationId__c = operationId,
            KGRenewal__Details__c = 'Test details',
            KGRenewal__IsSuccess__c = true,
            KGRenewal__Timestamp__c = String.valueOf(System.now())
        );
        insert testLog;

        // Verify logs can be retrieved
        List<KGRenewal__KineticGrowthLog__c> logs = BatchLogAnalyzer.getBatchLogs(operationId);
        System.assert(!logs.isEmpty(), 'Should be able to retrieve logs by operation ID');
    }
}