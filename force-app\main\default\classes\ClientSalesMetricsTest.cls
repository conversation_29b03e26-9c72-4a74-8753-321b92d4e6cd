@isTest
private class ClientSalesMetricsTest {
    
    @testSetup
    static void setupTestData() {
        // Create test accounts
        List<Account> testAccounts = new List<Account>();
        for (Integer i = 0; i < 5; i++) {
            testAccounts.add(new Account(
                Name = 'Test Account ' + i,
                Tenant_ID__c = 1000 + i
            ));
        }
        insert testAccounts;
        
        Date today = Date.today();
        
        // Create test opportunities
        List<Opportunity> testOpps = new List<Opportunity>();
        for (Integer i = 0; i < testAccounts.size(); i++) {
            testOpps.add(new Opportunity(
                Name = 'Test Opp ' + i,
                AccountId = testAccounts[i].Id,
                StageName = i < 2 ? 'Closed Won' : 'Negotiation/Review',
                CloseDate = today.addDays(30),
                Amount = 10000 + (i * 5000),
                KGRenewal__Term__c = 12
            ));
        }
        insert testOpps;
        
        // Create test subscriptions
        List<KGRenewal__Subscription__c> testSubs = new List<KGRenewal__Subscription__c>();
        testSubs.add(new KGRenewal__Subscription__c(
            KGR<PERSON>wal__Account__c = testAccounts[0].Id,
            KGRenewal__TotalAmount__c = 100000,
            KGRenewal__RenewalAmount__c = 90000,
            KGRenewal__AmountPaid__c = 60000,
            KGRenewal__StartDate__c = today.addDays(-100),
            KGRenewal__EndDate__c = today.addDays(265),
            KGRenewal__RenewalDate__c = today.addDays(200)
        ));
        testSubs.add(new KGRenewal__Subscription__c(
            KGRenewal__Account__c = testAccounts[1].Id,
            KGRenewal__TotalAmount__c = 50000,
            KGRenewal__RenewalAmount__c = 45000,
            KGRenewal__AmountPaid__c = 30000,
            KGRenewal__StartDate__c = today.addDays(-50),
            KGRenewal__EndDate__c = today.addDays(315),
            KGRenewal__RenewalDate__c = today.addDays(250)
        ));
        insert testSubs;
        
        // Create test subscription products
        List<KGRenewal__SubscriptionProduct__c> testSubProducts = new List<KGRenewal__SubscriptionProduct__c>();
        testSubProducts.add(new KGRenewal__SubscriptionProduct__c(
            KGRenewal__Subscription__c = testSubs[0].Id,
            KGRenewal__TotalPrice__c = 50000,
            KGRenewal__Term__c = 12,
            KGRenewal__Quantity__c = 1,
            KGRenewal__UnitPrice__c = 50000
        ));
        testSubProducts.add(new KGRenewal__SubscriptionProduct__c(
            KGRenewal__Subscription__c = testSubs[1].Id,
            KGRenewal__TotalPrice__c = 25000,
            KGRenewal__Term__c = 12,
            KGRenewal__Quantity__c = 1,
            KGRenewal__UnitPrice__c = 25000
        ));
        insert testSubProducts;
    }
    
    @isTest
    static void testCalculateMetrics() {
        List<Account> testAccounts = [SELECT Id FROM Account WHERE Name LIKE 'Test Account%'];
        List<Id> accountIds = new List<Id>();
        for (Account acc : testAccounts) {
            accountIds.add(acc.Id);
        }
        
        Test.startTest();
        Map<Id, Map<String, Object>> results = ClientSalesMetricsUtil.calculateMetrics(accountIds);
        Test.stopTest();
        
        System.assertNotEquals(null, results, 'Should return results');
        System.assertEquals(testAccounts.size(), results.size(), 'Should return metrics for all accounts');
        
        // Verify metrics for first account (has subscription and opportunity)
        Map<String, Object> metrics = results.get(testAccounts[0].Id);
        System.assertNotEquals(null, metrics, 'Should have metrics for first account');
        System.assertEquals(1, metrics.get('subscriptionCount'), 'Should have 1 subscription');
        System.assertEquals(1, metrics.get('opportunityCount'), 'Should have 1 opportunity');
    }
    
    @isTest
    static void testUpdateAccountSalesMetricsAction() {
        List<Account> testAccounts = [SELECT Id FROM Account WHERE Name LIKE 'Test Account%'];
        List<Id> accountIds = new List<Id>();
        for (Account acc : testAccounts) {
            accountIds.add(acc.Id);
        }
        
        Test.startTest();
        UpdateAccountSalesMetricsAction.updateMetrics(accountIds);
        Test.stopTest();
        
        // Verify accounts were updated
        List<Account> updatedAccounts = [SELECT Id, Active_Subscription_Countc__c, Total_Revenuec__c,
                                               MRRc__c, ARRc__c, Annual_Contract_Valuec__c,
                                               Closed_Won_ACVc__c, Proforma_Invoice_Valuec__c,
                                               Targeted_Construction_Volume_TCVc__c,
                                               Contract_Effective_Datec__c, Contract_End_Datec__c
                                        FROM Account WHERE Id IN :accountIds];
        
        System.assertEquals(testAccounts.size(), updatedAccounts.size(), 'Should update all accounts');
        
        // Verify first account has expected values
        Account firstAccount = updatedAccounts[0];
        System.assertEquals(1, firstAccount.Active_Subscription_Countc__c, 'Should have 1 subscription');
        System.assertNotEquals(null, firstAccount.Total_Revenuec__c, 'Should have revenue');
    }
    
    @isTest
    static void testCalculateMetricsWithNullInput() {
        Test.startTest();
        Map<Id, Map<String, Object>> results = ClientSalesMetricsUtil.calculateMetrics(null);
        Test.stopTest();

        System.assertNotEquals(null, results, 'Should return empty map for null input');
        System.assertEquals(0, results.size(), 'Should return empty map for null input');
    }
    
    @isTest
    static void testScheduler() {
        Test.startTest();
        UpdateAccountSalesMetricsScheduler scheduler = new UpdateAccountSalesMetricsScheduler();
        String cronExpression = '0 0 2 * * ?';
        System.schedule('Test Account Sales Metrics Update', cronExpression, scheduler);
        Test.stopTest();
        
        // Verify scheduled job was created
        List<CronTrigger> scheduledJobs = [SELECT Id, CronExpression, State FROM CronTrigger WHERE CronJobDetail.Name = 'Test Account Sales Metrics Update'];
        System.assertEquals(1, scheduledJobs.size(), 'Should have created scheduled job');
        System.assertEquals(cronExpression, scheduledJobs[0].CronExpression, 'Should have correct cron expression');
    }
    
    @isTest
    static void testCalculateMetricsWithEmptyInput() {
        Test.startTest();
        Map<Id, Map<String, Object>> results = ClientSalesMetricsUtil.calculateMetrics(new List<Id>());
        Test.stopTest();

        System.assertNotEquals(null, results, 'Should return empty map for empty input');
        System.assertEquals(0, results.size(), 'Should return empty map for empty input');
    }
    
    @isTest
    static void testCalculateMetricsWithNonExistentAccount() {
        // Test with account ID that doesn't exist
        List<Id> fakeAccountIds = new List<Id>();
        fakeAccountIds.add('001000000000000AAA'); // Fake account ID

        Test.startTest();
        Map<Id, Map<String, Object>> results = ClientSalesMetricsUtil.calculateMetrics(fakeAccountIds);
        Test.stopTest();

        System.assertNotEquals(null, results, 'Should return results even for non-existent accounts');
        System.assertEquals(1, results.size(), 'Should return entry for requested account ID');

        Map<String, Object> metrics = results.get(fakeAccountIds[0]);
        System.assertNotEquals(null, metrics, 'Should have metrics entry');
        System.assertEquals(null, metrics.get('account'), 'Account should be null for non-existent ID');
        System.assertEquals(0, metrics.get('subscriptionCount'), 'Should have 0 subscriptions');
        System.assertEquals(0, metrics.get('totalRevenue'), 'Should have 0 revenue');
    }

    @isTest
    static void testCalculateMetricsWithComplexData() {
        // Test with complex subscription and opportunity data
        List<Account> testAccounts = [SELECT Id FROM Account WHERE Name LIKE 'Test Account%'];
        Account testAccount = testAccounts[0];

        // Create additional opportunities with different stages
        List<Opportunity> additionalOpps = new List<Opportunity>();
        additionalOpps.add(new Opportunity(
            Name = 'Lost Opp',
            AccountId = testAccount.Id,
            StageName = 'Closed Lost',
            CloseDate = Date.today().addDays(-5),
            Amount = 15000
        ));
        additionalOpps.add(new Opportunity(
            Name = 'Proforma Opp',
            AccountId = testAccount.Id,
            StageName = 'Proforma Invoice',
            CloseDate = Date.today().addDays(10),
            Amount = 8000
        ));
        insert additionalOpps;

        Test.startTest();
        Map<Id, Map<String, Object>> results = ClientSalesMetricsUtil.calculateMetrics(new List<Id>{testAccount.Id});
        Test.stopTest();

        System.assertNotEquals(null, results, 'Should return results');
        Map<String, Object> metrics = results.get(testAccount.Id);
        System.assertNotEquals(null, metrics, 'Should have metrics for account');

        // Verify complex calculations
        System.assertEquals(1, metrics.get('lostCount'), 'Should count lost opportunities');
        System.assertEquals(15000, metrics.get('lostValue'), 'Should sum lost opportunity values');
        System.assertEquals(8000, metrics.get('proformaInvoiceValue'), 'Should sum proforma invoice values');
        System.assertEquals(1, metrics.get('proformaInvoiceCount'), 'Should count proforma invoices');
    }

    @isTest
    static void testCalculateMetricsWithContractDates() {
        // Test contract date calculations
        Account testAccount = new Account(Name = 'Contract Dates Test');
        insert testAccount;

        // Create opportunity with contract dates
        Opportunity opp = new Opportunity(
            Name = 'Contract Opp',
            AccountId = testAccount.Id,
            StageName = 'Closed Won',
            CloseDate = Date.today().addDays(-5),
            Amount = 50000,
            KGRenewal__ContractEffectiveDate__c = Date.today().addDays(-30),
            KGRenewal__ContractEndDate__c = Date.today().addDays(335),
            Targeted_Construction_Volume_TCV__c = 75000
        );
        insert opp;

        Test.startTest();
        Map<Id, Map<String, Object>> results = ClientSalesMetricsUtil.calculateMetrics(new List<Id>{testAccount.Id});
        Test.stopTest();

        System.assertNotEquals(null, results, 'Should return results');
        Map<String, Object> metrics = results.get(testAccount.Id);
        System.assertNotEquals(null, metrics, 'Should have metrics for account');

        // Verify contract date fields
        System.assertEquals(Date.today().addDays(-30), metrics.get('contractEffectiveDate'), 'Should have contract effective date');
        System.assertEquals(Date.today().addDays(335), metrics.get('contractEndDate'), 'Should have contract end date');
        System.assertEquals(75000, metrics.get('targetedConstructionVolumeTCV'), 'Should have TCV value');
    }

    @isTest
    static void testCalculateMetricsWithMRRARR() {
        // Test MRR and ARR calculations
        List<Account> testAccounts = [SELECT Id FROM Account WHERE Name LIKE 'Test Account%'];
        Account testAccount = testAccounts[0];

        Test.startTest();
        Map<Id, Map<String, Object>> results = ClientSalesMetricsUtil.calculateMetrics(new List<Id>{testAccount.Id});
        Test.stopTest();

        System.assertNotEquals(null, results, 'Should return results');
        Map<String, Object> metrics = results.get(testAccount.Id);
        System.assertNotEquals(null, metrics, 'Should have metrics for account');

        // Verify MRR and ARR calculations
        // Note: Since KGRenewal__IsCurrent__c is not writeable in tests, subscription products won't be found
        // So MRR and ARR will be 0 in test context
        System.assertEquals(0.00, metrics.get('mrr'), 'Should be 0 MRR in test context (IsCurrent field not writeable)');
        System.assertEquals(0.00, metrics.get('arr'), 'Should be 0 ARR in test context (IsCurrent field not writeable)');
    }
}
