<template>
    <lightning-card title="Client Revenue Summary">
      <template if:true={formattedTenantId}>
        <div style="position:absolute;top:18px;right:32px;font-weight:bold;font-size:1rem;background:white;padding:2px 8px;border-radius:6px;box-shadow:0 1px 4px #eee;">Tenant ID: {formattedTenantId}</div>
      </template>
      <div class="highlight-badge">
        <img src={clientTypeIcon} alt={clientType} class="pulsing {clientTypeClass} client-logo" />
        <span class="highlight-text {clientTypeClass}">{clientType}</span>
      </div>
      <div class="grid">
        <div class="item-card" onclick={handleExportARR} style="cursor:pointer;">
          <div class="icon-wrapper purple">
            <lightning-icon icon-name="utility:chart" size="x-small"></lightning-icon>
          </div>
          <div class="text-content">
            <span class="label">ARR</span>
            <p class="value">$ {formattedARR}</p>
          </div>
        </div>
        <div class="item-card" onclick={handleExportMRR} style="cursor:pointer;">
          <div class="icon-wrapper blue">
            <lightning-icon icon-name="utility:chart" size="x-small"></lightning-icon>
          </div>
          <div class="text-content">
            <span class="label">MRR</span>
            <p class="value">$ {formattedMRR}</p>
          </div>
        </div>
        <div class="item-card" onclick={handleExportACV} style="cursor:pointer;">
          <div class="icon-wrapper teal">
            <lightning-icon icon-name="utility:contract" size="x-small"></lightning-icon>
          </div>
          <div class="text-content">
            <span class="label">ACV</span>
            <p class="value">$ {formattedAnnualContractValue}</p>
          </div>
        </div>
        <div class="item-card" onclick={handleExportClosedWonACV} style="cursor:pointer;">
          <div class="icon-wrapper green">
            <lightning-icon icon-name="utility:check" size="x-small"></lightning-icon>
          </div>
          <div class="text-content">
            <span class="label">Closed Won ACV</span>
            <p class="value">{closedWonCount} ({formattedClosedWonACV})</p>
          </div>
        </div>
        <div class="item-card" onclick={handleExportProformaInvoice} style="cursor:pointer;">
          <div class="icon-wrapper orange">
            <lightning-icon icon-name="utility:money" size="x-small"></lightning-icon>
          </div>
          <div class="text-content">
            <span class="label">Proforma Invoice ACV</span>
            <p class="value">{proformaInvoiceCount} ($ {proformaInvoiceValue})</p>
          </div>
        </div>
        <div class="item-card" onclick={handleExportTotalOpportunities} style="cursor:pointer;">
          <div class="icon-wrapper gray">
            <lightning-icon icon-name="standard:opportunity" size="x-small"></lightning-icon>
          </div>
          <div class="text-content">
            <span class="label">Total Opportunities</span>
            <p class="value">{opportunityCount}</p>
          </div>
        </div>
        <div class="item-card" onclick={handleExportOpenOpportunities} style="cursor:pointer;">
          <div class="icon-wrapper orange">
            <lightning-icon icon-name="utility:open_folder" size="x-small"></lightning-icon>
          </div>
          <div class="text-content">
            <span class="label">Open Opportunities</span>
            <p class="value">{openOpportunities}</p>
          </div>
        </div>
        <div class="item-card" onclick={handleExportActiveSubscriptions} style="cursor:pointer;">
          <div class="icon-wrapper green">
            <lightning-icon icon-name="standard:product" size="x-small"></lightning-icon>
          </div>
          <div class="text-content">
            <span class="label">Active Subscriptions</span>
            <p class="value">{formattedSubscriptionCount}</p>
          </div>
        </div>
        <div class="item-card" onclick={handleExportTotalRevenue} style="cursor:pointer;">
          <div class="icon-wrapper blue">
            <lightning-icon icon-name="utility:money" size="x-small"></lightning-icon>
          </div>
          <div class="text-content">
            <span class="label">Total Closed Won</span>
            <p class="value">$ {formattedTotalRevenue}</p>
          </div>
        </div>
        <div class="item-card" onclick={handleExportRenewalAmount} style="cursor:pointer;">
          <div class="icon-wrapper purple">
            <lightning-icon icon-name="utility:refresh" size="x-small"></lightning-icon>
          </div>
          <div class="text-content">
            <span class="label">Renewal Amount</span>
            <p class="value">{renewalCount} ($ {formattedRenewalAmount})</p>
          </div>
        </div>
        <div class="item-card" onclick={handleExportAmountPaid} style="cursor:pointer;">
          <div class="icon-wrapper green">
            <lightning-icon icon-name="utility:check" size="x-small"></lightning-icon>
          </div>
          <div class="text-content">
            <span class="label">Amount Paid</span>
            <p class="value">$ {formattedTotalAmountPaid}</p>
          </div>
        </div>
        <div class="item-card" onclick={handleExportAmountRemaining} style="cursor:pointer;">
          <div class="icon-wrapper orange">
            <lightning-icon icon-name="utility:currency" size="x-small"></lightning-icon>
          </div>
          <div class="text-content">
            <span class="label">Amount Remaining</span>
            <p class="value">$ {formattedTotalAmountRemaining}</p>
          </div>
        </div>
        <div class="item-card" onclick={handleExportWonValue} style="cursor:pointer;">
          <div class="icon-wrapper green">
            <lightning-icon icon-name="utility:check" size="x-small"></lightning-icon>
          </div>
          <div class="text-content">
            <span class="label">Won Value</span>
            <p class="value">$ {formattedWonValue}</p>
          </div>
        </div>
        <div class="item-card" onclick={handleExportNegotiationValue} style="cursor:pointer;">
          <div class="icon-wrapper orange">
            <lightning-icon icon-name="utility:loop" size="x-small"></lightning-icon>
          </div>
          <div class="text-content">
            <span class="label">Value in Negotiation</span>
            <p class="value">$ {formattedNegotiationValue}</p>
          </div>
        </div>
        <div class="item-card" onclick={handleExportLost} style="cursor:pointer;">
          <div class="icon-wrapper red">
            <lightning-icon icon-name="utility:close" size="x-small"></lightning-icon>
          </div>
          <div class="text-content">
            <span class="label">Lost</span>
            <p class="value">{lostCount} ($ {formattedLostValue})</p>
          </div>
        </div>
      </div>
    </lightning-card>
    <div class="saasloader-footer">powered by SAASLoader™</div>
</template>