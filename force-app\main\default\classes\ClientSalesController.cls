public with sharing class ClientSalesController {
    @AuraEnabled(cacheable=true)
    public static Map<String, Object> getClientSalesData(Id accountId) {
        Map<Id, Map<String, Object>> allMetrics = ClientSalesMetricsUtil.calculateMetrics(new List<Id>{accountId});
        return allMetrics.containsKey(accountId) ? allMetrics.get(accountId) : new Map<String, Object>();
    }

    @AuraEnabled(cacheable=true)
    public static Map<String, Object> getOrgSalesDashboardData() {
        // Query all active Account IDs
        List<Id> allAccountIds = new List<Id>();
        for (Account acc : [SELECT Id FROM Account]) {
            allAccountIds.add(acc.Id);
        }
        if (allAccountIds.isEmpty()) return new Map<String, Object>();

        Map<Id, Map<String, Object>> allMetrics = ClientSalesMetricsUtil.calculateMetrics(allAccountIds);
        // Aggregate all metrics
        Map<String, Object> orgTotals = new Map<String, Object>();
        Decimal subscriptionCount = 0;
        Decimal totalRevenue = 0;
        Decimal totalRenewalAmount = 0;
        Decimal totalAmountPaid = 0;
        Decimal totalAmountRemaining = 0;
        Decimal averageSubscriptionValue = 0;
        Decimal opportunityCount = 0;
        Decimal openOpportunities = 0;
        Decimal wonValue = 0;
        Decimal lostValue = 0;
        Decimal lostCount = 0;
        Decimal negotiationValue = 0;
        Decimal annualContractValue = 0;
        Decimal mrr = 0;
        Decimal arr = 0;
        Set<Object> renewalDates = new Set<Object>();
        Integer accountCounter = 0;

        for (Map<String, Object> metrics : allMetrics.values()) {
            subscriptionCount += (Decimal)metrics.get('subscriptionCount');
            totalRevenue += (Decimal)metrics.get('totalRevenue');
            totalRenewalAmount += (Decimal)metrics.get('totalRenewalAmount');
            totalAmountPaid += (Decimal)metrics.get('totalAmountPaid');
            totalAmountRemaining += (Decimal)metrics.get('totalAmountRemaining');
            averageSubscriptionValue += (Decimal)metrics.get('averageSubscriptionValue');
            opportunityCount += (Decimal)metrics.get('opportunityCount');
            openOpportunities += (Decimal)metrics.get('openOpportunities');
            wonValue += (Decimal)metrics.get('wonValue');
            lostValue += (Decimal)metrics.get('lostValue');
            lostCount += (Decimal)metrics.get('lostCount');
            negotiationValue += (Decimal)metrics.get('negotiationValue');
            annualContractValue += (Decimal)metrics.get('annualContractValue');
            mrr += (Decimal)metrics.get('mrr');
            arr += (Decimal)metrics.get('arr');
            if (metrics.get('renewalDate') != null) renewalDates.add(metrics.get('renewalDate'));
            accountCounter++;
        }
        // For averageSubscriptionValue, use the mean across all accounts
        Decimal avgSubValue = accountCounter > 0 ? averageSubscriptionValue / accountCounter : 0;
        orgTotals.put('subscriptionCount', subscriptionCount);
        orgTotals.put('totalRevenue', totalRevenue);
        orgTotals.put('totalRenewalAmount', totalRenewalAmount);
        orgTotals.put('totalAmountPaid', totalAmountPaid);
        orgTotals.put('totalAmountRemaining', totalAmountRemaining);
        orgTotals.put('averageSubscriptionValue', avgSubValue.setScale(2));
        orgTotals.put('opportunityCount', opportunityCount);
        orgTotals.put('openOpportunities', openOpportunities);
        orgTotals.put('wonValue', wonValue);
        orgTotals.put('lostValue', lostValue);
        orgTotals.put('lostCount', lostCount);
        orgTotals.put('negotiationValue', negotiationValue);
        orgTotals.put('annualContractValue', annualContractValue);
        orgTotals.put('mrr', mrr);
        orgTotals.put('arr', arr);

        // Calculate open renewals
        Integer openRenewals = 0;
        List<KGRenewal__Subscription__c> renewalSubs = [
            SELECT KGRenewal__RenewalOpportunity__c, KGRenewal__RenewalOpportunity__r.Name, KGRenewal__RenewalOpportunity__r.IsClosed
            FROM KGRenewal__Subscription__c
            WHERE (KGRenewal__RenewalOpportunity__c != null OR KGRenewal__RenewalOpportunity__r.Name LIKE '%Renewal%')
        ];
        for (KGRenewal__Subscription__c sub : renewalSubs) {
            if (sub.KGRenewal__RenewalOpportunity__r != null && !sub.KGRenewal__RenewalOpportunity__r.IsClosed) {
                openRenewals++;
            }
        }
        orgTotals.put('openRenewals', openRenewals);

        // Calculate Proforma Invoice ACV and Closed Won ACV KPIs
        Integer proformaInvoiceCount = 0;
        Decimal proformaInvoiceACV = 0;
        Integer closedWonCount = 0;
        Decimal closedWonACV = 0;
        Date today = Date.today();
        // Proforma Invoice
        for (Opportunity o : [
            SELECT Amount, KGRenewal__Term__c, KGRenewal__ContractEffectiveDate__c, KGRenewal__ContractEndDate__c
            FROM Opportunity
            WHERE StageName = 'Proforma Invoice'
              AND KGRenewal__ContractEffectiveDate__c <= :today
              AND KGRenewal__ContractEndDate__c >= :today
        ]) {
            proformaInvoiceCount++;
            Decimal acv = 0;
            Decimal termYears;
            if (o.KGRenewal__Term__c != null && o.KGRenewal__Term__c > 0) {
                if (o.KGRenewal__Term__c == 12) {
                    acv = (o.Amount != null ? o.Amount : 0);
                } else {
                    termYears = o.KGRenewal__Term__c / 12;
                    if (o.Amount != null && termYears > 0) {
                        acv = o.Amount / termYears;
                    }
                }
            } else if (o.KGRenewal__ContractEffectiveDate__c != null && o.KGRenewal__ContractEndDate__c != null && o.KGRenewal__ContractEndDate__c > o.KGRenewal__ContractEffectiveDate__c) {
                termYears = ((Decimal) o.KGRenewal__ContractEndDate__c.daysBetween(o.KGRenewal__ContractEffectiveDate__c)) / 365;
                if (o.Amount != null && termYears > 0) {
                    acv = o.Amount / termYears;
                }
            }
            proformaInvoiceACV += acv;
        }
        orgTotals.put('proformaInvoiceCount', proformaInvoiceCount);
        orgTotals.put('proformaInvoiceValue', proformaInvoiceACV.setScale(2));
        // Closed Won
        for (Opportunity o : [
            SELECT Amount, KGRenewal__Term__c, KGRenewal__ContractEffectiveDate__c, KGRenewal__ContractEndDate__c
            FROM Opportunity
            WHERE StageName = 'Closed Won'
              AND KGRenewal__ContractEffectiveDate__c <= :today
              AND KGRenewal__ContractEndDate__c >= :today
        ]) {
            closedWonCount++;
            Decimal acv = 0;
            Decimal termYears;
            if (o.KGRenewal__Term__c != null && o.KGRenewal__Term__c > 0) {
                if (o.KGRenewal__Term__c == 12) {
                    acv = (o.Amount != null ? o.Amount : 0);
                } else {
                    termYears = o.KGRenewal__Term__c / 12;
                    if (o.Amount != null && termYears > 0) {
                        acv = o.Amount / termYears;
                    }
                }
            } else if (o.KGRenewal__ContractEffectiveDate__c != null && o.KGRenewal__ContractEndDate__c != null && o.KGRenewal__ContractEndDate__c > o.KGRenewal__ContractEffectiveDate__c) {
                termYears = ((Decimal) o.KGRenewal__ContractEndDate__c.daysBetween(o.KGRenewal__ContractEffectiveDate__c)) / 365;
                if (o.Amount != null && termYears > 0) {
                    acv = o.Amount / termYears;
                }
            }
            closedWonACV += acv;
        }
        orgTotals.put('closedWonCount', closedWonCount);
        orgTotals.put('closedWonACV', closedWonACV.setScale(2));

        return orgTotals;
    }

    @AuraEnabled(cacheable=true)
    public static List<Map<String, Object>> exportSubscriptionData(String exportType) {
        String fieldToExport;
        String label;
        if (exportType == 'TotalRevenue') {
            fieldToExport = 'KGRenewal__TotalAmount__c';
            label = 'KGRenewal__TotalAmount__c';
        } else if (exportType == 'RenewalAmount') {
            fieldToExport = 'KGRenewal__RenewalAmount__c';
            label = 'KGRenewal__RenewalAmount__c';
        } else if (exportType == 'AmountPaid') {
            fieldToExport = 'KGRenewal__AmountPaid__c';
            label = 'KGRenewal__AmountPaid__c';
        } else {
            // Default to TotalAmount if unknown
            fieldToExport = 'KGRenewal__TotalAmount__c';
            label = 'KGRenewal__TotalAmount__c';
        }
        List<KGRenewal__Subscription__c> subs = [
            SELECT Id, Name, KGRenewal__Account__c, KGRenewal__Account__r.Name, KGRenewal__TotalAmount__c, KGRenewal__RenewalAmount__c, KGRenewal__AmountPaid__c, KGRenewal__StartDate__c, KGRenewal__EndDate__c
            FROM KGRenewal__Subscription__c
            WHERE KGRenewal__IsActiveTerm__c = true
        ];
        List<Map<String, Object>> results = new List<Map<String, Object>>();
        for (KGRenewal__Subscription__c s : subs) {
            Map<String, Object> row = new Map<String, Object>();
            row.put('Id', s.Id);
            row.put('Name', s.Name);
            row.put('AccountId', s.KGRenewal__Account__c);
            row.put('AccountName', s.KGRenewal__Account__r != null ? s.KGRenewal__Account__r.Name : null);
            row.put('KGRenewal__TotalAmount__c', s.KGRenewal__TotalAmount__c);
            row.put('KGRenewal__RenewalAmount__c', s.KGRenewal__RenewalAmount__c);
            row.put('KGRenewal__AmountPaid__c', s.KGRenewal__AmountPaid__c);
            row.put('KGRenewal__StartDate__c', s.KGRenewal__StartDate__c);
            row.put('KGRenewal__EndDate__c', s.KGRenewal__EndDate__c);
            // Only keep the relevant field for the export type
            Map<String, Object> filteredRow = new Map<String, Object>();
            filteredRow.put('Id', row.get('Id'));
            filteredRow.put('Name', row.get('Name'));
            filteredRow.put('AccountId', row.get('AccountId'));
            filteredRow.put('AccountName', row.get('AccountName'));
            filteredRow.put(label, row.get(label));
            filteredRow.put('KGRenewal__StartDate__c', row.get('KGRenewal__StartDate__c'));
            filteredRow.put('KGRenewal__EndDate__c', row.get('KGRenewal__EndDate__c'));
            results.add(filteredRow);
        }
        return results;
    }

    @AuraEnabled(cacheable=true)
    public static List<Map<String, Object>> exportActiveSubscriptions(Id accountId) {
        String baseQuery = 'SELECT Id, KGRenewal__IsActive__c, Name, KGRenewal__Account__c, KGRenewal__Account__r.Name, KGRenewal__TotalAmount__c, KGRenewal__StartDate__c, KGRenewal__EndDate__c, KGRenewal__RenewalAmount__c, KGRenewal__AmountPaid__c, KGRenewal__AmountRemaining__c, KGRenewal__RenewalOpportunity__c, KGRenewal__RenewalOpportunity__r.Name FROM KGRenewal__Subscription__c WHERE KGRenewal__IsActiveTerm__c = true';
        if (accountId != null) {
            baseQuery += ' AND KGRenewal__Account__c = \'' + String.escapeSingleQuotes(accountId) + '\'';
        }
        List<KGRenewal__Subscription__c> subs = Database.query(baseQuery);
        List<Map<String, Object>> results = new List<Map<String, Object>>();
        for (KGRenewal__Subscription__c s : subs) {
            Map<String, Object> row = new Map<String, Object>();
            row.put('Id', s.Id);
            row.put('KGRenewal__IsActive__c', s.KGRenewal__IsActive__c ? 'True' : 'False');
            row.put('Name', s.Name);
            row.put('AccountId', s.KGRenewal__Account__c);
            row.put('AccountName', s.KGRenewal__Account__r != null ? s.KGRenewal__Account__r.Name : null);
            row.put('KGRenewal__TotalAmount__c', s.KGRenewal__TotalAmount__c);
            row.put('KGRenewal__StartDate__c', s.KGRenewal__StartDate__c);
            row.put('KGRenewal__EndDate__c', s.KGRenewal__EndDate__c);
            row.put('KGRenewal__RenewalAmount__c', s.KGRenewal__RenewalAmount__c);
            row.put('KGRenewal__AmountPaid__c', s.KGRenewal__AmountPaid__c);
            row.put('KGRenewal__AmountRemaining__c', s.KGRenewal__AmountRemaining__c);
            row.put('KGRenewal__RenewalOpportunity__c', s.KGRenewal__RenewalOpportunity__c);
            row.put('RenewalOpportunityName', s.KGRenewal__RenewalOpportunity__r != null ? s.KGRenewal__RenewalOpportunity__r.Name : null);
            results.add(row);
        }
        return results;
    }

    @AuraEnabled(cacheable=true)
    public static List<Map<String, Object>> exportActiveACVOpportunities(Id accountId) {
        Date today = Date.today();
        String baseQuery = 'SELECT Id, Name, AccountId, Account.Name, Amount, StageName, KGRenewal__ContractEffectiveDate__c, KGRenewal__ContractEndDate__c, KGRenewal__Term__c, IsClosed FROM Opportunity WHERE (KGRenewal__ContractEffectiveDate__c <= :today AND KGRenewal__ContractEndDate__c >= :today) AND (StageName = \'Closed Won\' OR StageName = \'Proforma Invoice\')';
        if (accountId != null) {
            baseQuery += ' AND AccountId = \'' + String.escapeSingleQuotes(accountId) + '\'';
        }
        List<Opportunity> opps = Database.query(baseQuery);
        List<Map<String, Object>> results = new List<Map<String, Object>>();
        for (Opportunity o : opps) {
            Map<String, Object> row = new Map<String, Object>();
            row.put('Id', o.Id);
            row.put('Name', o.Name);
            row.put('AccountId', o.AccountId);
            row.put('AccountName', o.Account != null ? o.Account.Name : null);
            row.put('Amount', o.Amount);
            // --- ACV Calculation ---
            Decimal acv = 0;
            Decimal termYears;
            if (o.KGRenewal__Term__c != null && o.KGRenewal__Term__c > 0) {
                if (o.KGRenewal__Term__c == 12) {
                    acv = (o.Amount != null ? o.Amount : 0);
                } else {
                    termYears = o.KGRenewal__Term__c / 12;
                    if (o.Amount != null && termYears > 0) {
                        acv = o.Amount / termYears;
                    }
                }
            } else if (o.KGRenewal__ContractEffectiveDate__c != null && o.KGRenewal__ContractEndDate__c != null && o.KGRenewal__ContractEndDate__c > o.KGRenewal__ContractEffectiveDate__c) {
                termYears = ((Decimal) o.KGRenewal__ContractEndDate__c.daysBetween(o.KGRenewal__ContractEffectiveDate__c)) / 365;
                if (o.Amount != null && termYears > 0) {
                    acv = o.Amount / termYears;
                }
            }
            row.put('ACV', acv.setScale(2));
            row.put('StageName', o.StageName);
            row.put('KGRenewal__ContractEffectiveDate__c', o.KGRenewal__ContractEffectiveDate__c);
            row.put('KGRenewal__ContractEndDate__c', o.KGRenewal__ContractEndDate__c);
            row.put('KGRenewal__Term__c', o.KGRenewal__Term__c);
            row.put('IsClosed', o.IsClosed);
            results.add(row);
        }
        return results;
    }

    @AuraEnabled(cacheable=true)
    public static List<Map<String, Object>> exportActiveMRR(Id accountId) {
        String baseQuery = 'SELECT Id, Name, KGRenewal__Subscription__c, KGRenewal__Subscription__r.Name, KGRenewal__Subscription__r.KGRenewal__Account__c, KGRenewal__Subscription__r.KGRenewal__Account__r.Name, KGRenewal__TotalPrice__c, KGRenewal__Term__c, KGRenewal__IsCurrent__c, KGRenewal__Subscription__r.KGRenewal__RenewalOpportunity__c, KGRenewal__Subscription__r.KGRenewal__RenewalOpportunity__r.Name FROM KGRenewal__SubscriptionProduct__c WHERE KGRenewal__IsCurrent__c = true AND KGRenewal__Subscription__r.KGRenewal__IsActiveTerm__c = true';
        if (accountId != null) {
            baseQuery += ' AND KGRenewal__Subscription__r.KGRenewal__Account__c = \'' + String.escapeSingleQuotes(accountId) + '\'';
        }
        List<KGRenewal__SubscriptionProduct__c> products = Database.query(baseQuery);
        List<Map<String, Object>> results = new List<Map<String, Object>>();
        for (KGRenewal__SubscriptionProduct__c p : products) {
            Map<String, Object> row = new Map<String, Object>();
            row.put('Id', p.Id);
            row.put('Name', p.Name);
            row.put('KGRenewal__Subscription__c', p.KGRenewal__Subscription__c);
            row.put('SubscriptionName', p.KGRenewal__Subscription__r != null ? p.KGRenewal__Subscription__r.Name : null);
            row.put('AccountId', p.KGRenewal__Subscription__r != null ? p.KGRenewal__Subscription__r.KGRenewal__Account__c : null);
            row.put('AccountName', (p.KGRenewal__Subscription__r != null && p.KGRenewal__Subscription__r.KGRenewal__Account__r != null) ? p.KGRenewal__Subscription__r.KGRenewal__Account__r.Name : null);
            row.put('KGRenewal__TotalPrice__c', p.KGRenewal__TotalPrice__c);
            row.put('KGRenewal__Term__c', p.KGRenewal__Term__c);
            row.put('KGRenewal__IsCurrent__c', p.KGRenewal__IsCurrent__c ? 'True' : 'False');
            // MRR = TotalPrice / Term
            Decimal mrr = (p.KGRenewal__TotalPrice__c != null && p.KGRenewal__Term__c != null && p.KGRenewal__Term__c > 0) ? p.KGRenewal__TotalPrice__c / p.KGRenewal__Term__c : 0;
            row.put('MRR', mrr.setScale(2));
            row.put('RenewalOpportunityId', (p.KGRenewal__Subscription__r != null) ? p.KGRenewal__Subscription__r.KGRenewal__RenewalOpportunity__c : null);
            row.put('RenewalOpportunityName', (p.KGRenewal__Subscription__r != null && p.KGRenewal__Subscription__r.KGRenewal__RenewalOpportunity__r != null) ? p.KGRenewal__Subscription__r.KGRenewal__RenewalOpportunity__r.Name : null);
            results.add(row);
        }
        return results;
    }

    @AuraEnabled(cacheable=true)
    public static List<Map<String, Object>> exportActiveARR(Id accountId) {
        String baseQuery = 'SELECT Id, Name, KGRenewal__Subscription__c, KGRenewal__Subscription__r.Name, KGRenewal__Subscription__r.KGRenewal__Account__c, KGRenewal__Subscription__r.KGRenewal__Account__r.Name, KGRenewal__TotalPrice__c, KGRenewal__Term__c, KGRenewal__IsCurrent__c, KGRenewal__Subscription__r.KGRenewal__RenewalOpportunity__c, KGRenewal__Subscription__r.KGRenewal__RenewalOpportunity__r.Name FROM KGRenewal__SubscriptionProduct__c WHERE KGRenewal__IsCurrent__c = true AND KGRenewal__Subscription__r.KGRenewal__IsActiveTerm__c = true';
        if (accountId != null) {
            baseQuery += ' AND KGRenewal__Subscription__r.KGRenewal__Account__c = \'' + String.escapeSingleQuotes(accountId) + '\'';
        }
        List<KGRenewal__SubscriptionProduct__c> products = Database.query(baseQuery);
        List<Map<String, Object>> results = new List<Map<String, Object>>();
        for (KGRenewal__SubscriptionProduct__c p : products) {
            Map<String, Object> row = new Map<String, Object>();
            row.put('Id', p.Id);
            row.put('Name', p.Name);
            row.put('KGRenewal__Subscription__c', p.KGRenewal__Subscription__c);
            row.put('SubscriptionName', p.KGRenewal__Subscription__r != null ? p.KGRenewal__Subscription__r.Name : null);
            row.put('AccountId', p.KGRenewal__Subscription__r != null ? p.KGRenewal__Subscription__r.KGRenewal__Account__c : null);
            row.put('AccountName', (p.KGRenewal__Subscription__r != null && p.KGRenewal__Subscription__r.KGRenewal__Account__r != null) ? p.KGRenewal__Subscription__r.KGRenewal__Account__r.Name : null);
            row.put('KGRenewal__TotalPrice__c', p.KGRenewal__TotalPrice__c);
            row.put('KGRenewal__Term__c', p.KGRenewal__Term__c);
            row.put('KGRenewal__IsCurrent__c', p.KGRenewal__IsCurrent__c ? 'True' : 'False');
            // MRR = TotalPrice / Term, ARR = MRR * 12
            Decimal mrr = (p.KGRenewal__TotalPrice__c != null && p.KGRenewal__Term__c != null && p.KGRenewal__Term__c > 0) ? p.KGRenewal__TotalPrice__c / p.KGRenewal__Term__c : 0;
            Decimal arr = mrr * 12;
            row.put('MRR', mrr.setScale(2));
            row.put('ARR', arr.setScale(2));
            row.put('RenewalOpportunityId', (p.KGRenewal__Subscription__r != null) ? p.KGRenewal__Subscription__r.KGRenewal__RenewalOpportunity__c : null);
            row.put('RenewalOpportunityName', (p.KGRenewal__Subscription__r != null && p.KGRenewal__Subscription__r.KGRenewal__RenewalOpportunity__r != null) ? p.KGRenewal__Subscription__r.KGRenewal__RenewalOpportunity__r.Name : null);
            results.add(row);
        }
        return results;
    }

    @AuraEnabled(cacheable=true)
    public static List<Map<String, Object>> exportTotalRevenue(Id accountId) {
        String baseQuery = 'SELECT Id, Name, KGRenewal__Account__c, KGRenewal__Account__r.Name, KGRenewal__TotalAmount__c, KGRenewal__StartDate__c, KGRenewal__EndDate__c FROM KGRenewal__Subscription__c WHERE KGRenewal__IsActiveTerm__c = true';
        if (accountId != null) {
            baseQuery += ' AND KGRenewal__Account__c = \'' + String.escapeSingleQuotes(accountId) + '\'';
        }
        List<KGRenewal__Subscription__c> subs = Database.query(baseQuery);
        List<Map<String, Object>> results = new List<Map<String, Object>>();
        for (KGRenewal__Subscription__c s : subs) {
            Map<String, Object> row = new Map<String, Object>();
            row.put('Id', s.Id);
            row.put('Name', s.Name);
            row.put('AccountId', s.KGRenewal__Account__c);
            row.put('AccountName', s.KGRenewal__Account__r != null ? s.KGRenewal__Account__r.Name : null);
            row.put('KGRenewal__TotalAmount__c', s.KGRenewal__TotalAmount__c);
            row.put('KGRenewal__StartDate__c', s.KGRenewal__StartDate__c);
            row.put('KGRenewal__EndDate__c', s.KGRenewal__EndDate__c);
            results.add(row);
        }
        return results;
    }

    @AuraEnabled(cacheable=true)
    public static List<Map<String, Object>> exportRenewalAmount(Id accountId) {
        String baseQuery = 'SELECT Id, Name, KGRenewal__Account__c, KGRenewal__Account__r.Name, KGRenewal__RenewalAmount__c, KGRenewal__StartDate__c, KGRenewal__EndDate__c FROM KGRenewal__Subscription__c WHERE KGRenewal__IsActiveTerm__c = true';
        if (accountId != null) {
            baseQuery += ' AND KGRenewal__Account__c = \'' + String.escapeSingleQuotes(accountId) + '\'';
        }
        List<KGRenewal__Subscription__c> subs = Database.query(baseQuery);
        List<Map<String, Object>> results = new List<Map<String, Object>>();
        for (KGRenewal__Subscription__c s : subs) {
            Map<String, Object> row = new Map<String, Object>();
            row.put('Id', s.Id);
            row.put('Name', s.Name);
            row.put('AccountId', s.KGRenewal__Account__c);
            row.put('AccountName', s.KGRenewal__Account__r != null ? s.KGRenewal__Account__r.Name : null);
            row.put('KGRenewal__RenewalAmount__c', s.KGRenewal__RenewalAmount__c);
            row.put('KGRenewal__StartDate__c', s.KGRenewal__StartDate__c);
            row.put('KGRenewal__EndDate__c', s.KGRenewal__EndDate__c);
            results.add(row);
        }
        return results;
    }

    @AuraEnabled(cacheable=true)
    public static List<Map<String, Object>> exportAmountPaid(Id accountId) {
        String baseQuery = 'SELECT Id, Name, KGRenewal__Account__c, KGRenewal__Account__r.Name, KGRenewal__AmountPaid__c, KGRenewal__StartDate__c, KGRenewal__EndDate__c FROM KGRenewal__Subscription__c WHERE KGRenewal__IsActiveTerm__c = true';
        if (accountId != null) {
            baseQuery += ' AND KGRenewal__Account__c = \'' + String.escapeSingleQuotes(accountId) + '\'';
        }
        List<KGRenewal__Subscription__c> subs = Database.query(baseQuery);
        List<Map<String, Object>> results = new List<Map<String, Object>>();
        for (KGRenewal__Subscription__c s : subs) {
            Map<String, Object> row = new Map<String, Object>();
            row.put('Id', s.Id);
            row.put('Name', s.Name);
            row.put('AccountId', s.KGRenewal__Account__c);
            row.put('AccountName', s.KGRenewal__Account__r != null ? s.KGRenewal__Account__r.Name : null);
            row.put('KGRenewal__AmountPaid__c', s.KGRenewal__AmountPaid__c);
            row.put('KGRenewal__StartDate__c', s.KGRenewal__StartDate__c);
            row.put('KGRenewal__EndDate__c', s.KGRenewal__EndDate__c);
            results.add(row);
        }
        return results;
    }

    @AuraEnabled(cacheable=true)
    public static List<Map<String, Object>> exportAmountRemaining(Id accountId) {
        String baseQuery = 'SELECT Id, Name, KGRenewal__Account__c, KGRenewal__Account__r.Name, KGRenewal__AmountRemaining__c, KGRenewal__StartDate__c, KGRenewal__EndDate__c FROM KGRenewal__Subscription__c WHERE KGRenewal__IsActiveTerm__c = true';
        if (accountId != null) {
            baseQuery += ' AND KGRenewal__Account__c = \'' + String.escapeSingleQuotes(accountId) + '\'';
        }
        List<KGRenewal__Subscription__c> subs = Database.query(baseQuery);
        List<Map<String, Object>> results = new List<Map<String, Object>>();
        for (KGRenewal__Subscription__c s : subs) {
            Map<String, Object> row = new Map<String, Object>();
            row.put('Id', s.Id);
            row.put('Name', s.Name);
            row.put('AccountId', s.KGRenewal__Account__c);
            row.put('AccountName', s.KGRenewal__Account__r != null ? s.KGRenewal__Account__r.Name : null);
            row.put('KGRenewal__AmountRemaining__c', s.KGRenewal__AmountRemaining__c);
            row.put('KGRenewal__StartDate__c', s.KGRenewal__StartDate__c);
            row.put('KGRenewal__EndDate__c', s.KGRenewal__EndDate__c);
            results.add(row);
        }
        return results;
    }

    @AuraEnabled(cacheable=true)
    public static List<Map<String, Object>> exportOpenRenewals(Id accountId) {
        String baseQuery = 'SELECT Id, Name, KGRenewal__Account__c, KGRenewal__Account__r.Name, KGRenewal__RenewalOpportunity__c, KGRenewal__RenewalOpportunity__r.Name, KGRenewal__RenewalOpportunity__r.IsClosed FROM KGRenewal__Subscription__c WHERE (KGRenewal__RenewalOpportunity__c != null OR KGRenewal__RenewalOpportunity__r.Name LIKE \'%Renewal%\')';
        if (accountId != null) {
            baseQuery += ' AND KGRenewal__Account__c = \'' + String.escapeSingleQuotes(accountId) + '\'';
        }
        List<KGRenewal__Subscription__c> subs = Database.query(baseQuery);
        List<Map<String, Object>> results = new List<Map<String, Object>>();
        for (KGRenewal__Subscription__c s : subs) {
            if (s.KGRenewal__RenewalOpportunity__r != null && !s.KGRenewal__RenewalOpportunity__r.IsClosed) {
                Map<String, Object> row = new Map<String, Object>();
                row.put('Id', s.Id);
                row.put('Name', s.Name);
                row.put('AccountId', s.KGRenewal__Account__c);
                row.put('AccountName', s.KGRenewal__Account__r != null ? s.KGRenewal__Account__r.Name : null);
                row.put('RenewalOpportunityId', s.KGRenewal__RenewalOpportunity__c);
                row.put('RenewalOpportunityName', s.KGRenewal__RenewalOpportunity__r.Name);
                results.add(row);
            }
        }
        return results;
    }

    @AuraEnabled(cacheable=true)
    public static List<Map<String, Object>> exportTotalOpportunities(Id accountId) {
        String baseQuery = 'SELECT Id, Name, AccountId, Account.Name, Amount, StageName, IsClosed FROM Opportunity';
        if (accountId != null) {
            baseQuery += ' WHERE AccountId = \'' + String.escapeSingleQuotes(accountId) + '\'';
        }
        List<Opportunity> opps = Database.query(baseQuery);
        List<Map<String, Object>> results = new List<Map<String, Object>>();
        for (Opportunity o : opps) {
            Map<String, Object> row = new Map<String, Object>();
            row.put('Id', o.Id);
            row.put('Name', o.Name);
            row.put('AccountId', o.AccountId);
            row.put('AccountName', o.Account != null ? o.Account.Name : null);
            row.put('Amount', o.Amount);
            row.put('StageName', o.StageName);
            row.put('IsClosed', o.IsClosed);
            results.add(row);
        }
        return results;
    }

    @AuraEnabled(cacheable=true)
    public static List<Map<String, Object>> exportOpenOpportunities(Id accountId) {
        String baseQuery = 'SELECT Id, Name, AccountId, Account.Name, Amount, StageName, IsClosed FROM Opportunity WHERE IsClosed = false';
        if (accountId != null) {
            baseQuery += ' AND AccountId = \'' + String.escapeSingleQuotes(accountId) + '\'';
        }
        List<Opportunity> opps = Database.query(baseQuery);
        List<Map<String, Object>> results = new List<Map<String, Object>>();
        for (Opportunity o : opps) {
            Map<String, Object> row = new Map<String, Object>();
            row.put('Id', o.Id);
            row.put('Name', o.Name);
            row.put('AccountId', o.AccountId);
            row.put('AccountName', o.Account != null ? o.Account.Name : null);
            row.put('Amount', o.Amount);
            row.put('StageName', o.StageName);
            row.put('IsClosed', o.IsClosed);
            results.add(row);
        }
        return results;
    }

    @AuraEnabled(cacheable=true)
    public static List<Map<String, Object>> exportWonValue(Id accountId) {
        String baseQuery = 'SELECT Id, Name, AccountId, Account.Name, Amount, StageName, IsClosed FROM Opportunity WHERE StageName = \'Closed Won\'';
        if (accountId != null) {
            baseQuery += ' AND AccountId = \'' + String.escapeSingleQuotes(accountId) + '\'';
        }
        List<Opportunity> opps = Database.query(baseQuery);
        List<Map<String, Object>> results = new List<Map<String, Object>>();
        for (Opportunity o : opps) {
            Map<String, Object> row = new Map<String, Object>();
            row.put('Id', o.Id);
            row.put('Name', o.Name);
            row.put('AccountId', o.AccountId);
            row.put('AccountName', o.Account != null ? o.Account.Name : null);
            row.put('Amount', o.Amount);
            row.put('StageName', o.StageName);
            row.put('IsClosed', o.IsClosed);
            results.add(row);
        }
        return results;
    }

    @AuraEnabled(cacheable=true)
    public static List<Map<String, Object>> exportNegotiationValue(Id accountId) {
        String baseQuery = 'SELECT Id, Name, AccountId, Account.Name, Amount, StageName, IsClosed FROM Opportunity WHERE StageName != \'Closed Won\' AND StageName != \'Closed Lost\' AND IsClosed = false';
        if (accountId != null) {
            baseQuery += ' AND AccountId = \'' + String.escapeSingleQuotes(accountId) + '\'';
        }
        List<Opportunity> opps = Database.query(baseQuery);
        List<Map<String, Object>> results = new List<Map<String, Object>>();
        for (Opportunity o : opps) {
            Map<String, Object> row = new Map<String, Object>();
            row.put('Id', o.Id);
            row.put('Name', o.Name);
            row.put('AccountId', o.AccountId);
            row.put('AccountName', o.Account != null ? o.Account.Name : null);
            row.put('Amount', o.Amount);
            row.put('StageName', o.StageName);
            row.put('IsClosed', o.IsClosed);
            results.add(row);
        }
        return results;
    }

    @AuraEnabled(cacheable=true)
    public static List<Map<String, Object>> exportLost(Id accountId) {
        String baseQuery = 'SELECT Id, Name, AccountId, Account.Name, Amount, StageName, IsClosed FROM Opportunity WHERE StageName = \'Closed Lost\'';
        if (accountId != null) {
            baseQuery += ' AND AccountId = \'' + String.escapeSingleQuotes(accountId) + '\'';
        }
        List<Opportunity> opps = Database.query(baseQuery);
        List<Map<String, Object>> results = new List<Map<String, Object>>();
        for (Opportunity o : opps) {
            Map<String, Object> row = new Map<String, Object>();
            row.put('Id', o.Id);
            row.put('Name', o.Name);
            row.put('AccountId', o.AccountId);
            row.put('AccountName', o.Account != null ? o.Account.Name : null);
            row.put('Amount', o.Amount);
            row.put('StageName', o.StageName);
            row.put('IsClosed', o.IsClosed);
            results.add(row);
        }
        return results;
    }

    @AuraEnabled(cacheable=true)
    public static List<Map<String, Object>> exportTotalTenantId() {
        List<Account> accounts = [
            SELECT Id, Name
            FROM Account
        ];
        List<Map<String, Object>> results = new List<Map<String, Object>>();
        for (Account a : accounts) {
            Map<String, Object> row = new Map<String, Object>();
            row.put('Id', a.Id);
            row.put('Name', a.Name);
            results.add(row);
        }
        return results;
    }

    @AuraEnabled(cacheable=true)
    public static List<Map<String, Object>> exportProformaInvoice(Id accountId) {
        Date today = Date.today();
        String baseQuery = 'SELECT Id, Name, AccountId, Account.Name, Amount, StageName, KGRenewal__ContractEffectiveDate__c, KGRenewal__ContractEndDate__c, KGRenewal__Term__c, IsClosed FROM Opportunity WHERE StageName = \'Proforma Invoice\' AND KGRenewal__ContractEffectiveDate__c <= :today AND KGRenewal__ContractEndDate__c >= :today';
        if (accountId != null) {
            baseQuery += ' AND AccountId = \'' + String.escapeSingleQuotes(accountId) + '\'';
        }
        List<Opportunity> opps = Database.query(baseQuery);
        List<Map<String, Object>> results = new List<Map<String, Object>>();
        for (Opportunity o : opps) {
            Map<String, Object> row = new Map<String, Object>();
            row.put('Id', o.Id);
            row.put('Name', o.Name);
            row.put('AccountId', o.AccountId);
            row.put('AccountName', o.Account != null ? o.Account.Name : null);
            row.put('Amount', o.Amount);
            // --- ACV Calculation ---
            Decimal acv = 0;
            Decimal termYears;
            if (o.KGRenewal__Term__c != null && o.KGRenewal__Term__c > 0) {
                if (o.KGRenewal__Term__c == 12) {
                    acv = (o.Amount != null ? o.Amount : 0);
                } else {
                    termYears = o.KGRenewal__Term__c / 12;
                    if (o.Amount != null && termYears > 0) {
                        acv = o.Amount / termYears;
                    }
                }
            } else if (o.KGRenewal__ContractEffectiveDate__c != null && o.KGRenewal__ContractEndDate__c != null && o.KGRenewal__ContractEndDate__c > o.KGRenewal__ContractEffectiveDate__c) {
                termYears = ((Decimal) o.KGRenewal__ContractEndDate__c.daysBetween(o.KGRenewal__ContractEffectiveDate__c)) / 365;
                if (o.Amount != null && termYears > 0) {
                    acv = o.Amount / termYears;
                }
            }
            row.put('ACV', acv.setScale(2));
            row.put('StageName', o.StageName);
            row.put('KGRenewal__ContractEffectiveDate__c', o.KGRenewal__ContractEffectiveDate__c);
            row.put('KGRenewal__ContractEndDate__c', o.KGRenewal__ContractEndDate__c);
            row.put('KGRenewal__Term__c', o.KGRenewal__Term__c);
            row.put('IsClosed', o.IsClosed);
            results.add(row);
        }
        return results;
    }

    @AuraEnabled(cacheable=true)
    public static List<Map<String, Object>> exportClosedWonACV(Id accountId) {
        Date today = Date.today();
        String baseQuery = 'SELECT Id, Name, AccountId, Account.Name, Amount, StageName, KGRenewal__ContractEffectiveDate__c, KGRenewal__ContractEndDate__c, KGRenewal__Term__c, IsClosed FROM Opportunity WHERE StageName = \'Closed Won\' AND KGRenewal__ContractEffectiveDate__c <= :today AND KGRenewal__ContractEndDate__c >= :today';
        if (accountId != null) {
            baseQuery += ' AND AccountId = \'' + String.escapeSingleQuotes(accountId) + '\'';
        }
        List<Opportunity> opps = Database.query(baseQuery);
        List<Map<String, Object>> results = new List<Map<String, Object>>();
        for (Opportunity o : opps) {
            Map<String, Object> row = new Map<String, Object>();
            row.put('Id', o.Id);
            row.put('Name', o.Name);
            row.put('AccountId', o.AccountId);
            row.put('AccountName', o.Account != null ? o.Account.Name : null);
            row.put('Amount', o.Amount);
            // --- ACV Calculation ---
            Decimal acv = 0;
            Decimal termYears;
            if (o.KGRenewal__Term__c != null && o.KGRenewal__Term__c > 0) {
                if (o.KGRenewal__Term__c == 12) {
                    acv = (o.Amount != null ? o.Amount : 0);
                } else {
                    termYears = o.KGRenewal__Term__c / 12;
                    if (o.Amount != null && termYears > 0) {
                        acv = o.Amount / termYears;
                    }
                }
            } else if (o.KGRenewal__ContractEffectiveDate__c != null && o.KGRenewal__ContractEndDate__c != null && o.KGRenewal__ContractEndDate__c > o.KGRenewal__ContractEffectiveDate__c) {
                termYears = ((Decimal) o.KGRenewal__ContractEndDate__c.daysBetween(o.KGRenewal__ContractEffectiveDate__c)) / 365;
                if (o.Amount != null && termYears > 0) {
                    acv = o.Amount / termYears;
                }
            }
            row.put('ACV', acv.setScale(2));
            row.put('StageName', o.StageName);
            row.put('KGRenewal__ContractEffectiveDate__c', o.KGRenewal__ContractEffectiveDate__c);
            row.put('KGRenewal__ContractEndDate__c', o.KGRenewal__ContractEndDate__c);
            row.put('KGRenewal__Term__c', o.KGRenewal__Term__c);
            row.put('IsClosed', o.IsClosed);
            results.add(row);
        }
        return results;
    }

    @AuraEnabled(cacheable=true)
    public static String getClientType(Id accountId) {
        if (accountId == null) {
            return 'Standard Client';
        }
        
        // Query subscription products for this account
        List<KGRenewal__SubscriptionProduct__c> products = [
            SELECT Id, Name, KGRenewal__Subscription__c, KGRenewal__Subscription__r.KGRenewal__Account__c
            FROM KGRenewal__SubscriptionProduct__c
            WHERE KGRenewal__Subscription__r.KGRenewal__Account__c = :accountId
              AND KGRenewal__IsCurrent__c = true
              AND KGRenewal__Subscription__r.KGRenewal__IsActiveTerm__c = true
        ];
        
        // Check if any product name contains 'unlimited' (case insensitive)
        for (KGRenewal__SubscriptionProduct__c product : products) {
            if (product.Name != null && product.Name.toLowerCase().contains('unlimited')) {
                return 'Unlimited Client';
            }
        }
        
        return 'Standard Client';
    }
}